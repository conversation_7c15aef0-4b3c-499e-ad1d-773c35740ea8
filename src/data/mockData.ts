import { ScrapedData, MetricData, ChartData, CategoryData, CompetitorData } from '../types';

export const mockScrapedData: ScrapedData[] = [
  {
    id: '1',
    title: 'Premium Wireless Headphones',
    description: 'High-quality wireless headphones with noise cancellation',
    url: 'https://example.com/product/1',
    category: 'Electronics',
    price: 299.99,
    rating: 4.5,
    timestamp: '2024-01-15T10:30:00Z',
    source: 'Amazon',
    competitor: 'TechCorp',
    tags: ['wireless', 'headphones', 'premium'],
    metadata: { brand: 'TechBrand', model: 'TB-WH500' }
  },
  {
    id: '2',
    title: 'Smart Fitness Watch',
    description: 'Advanced fitness tracking with heart rate monitoring',
    url: 'https://example.com/product/2',
    category: 'Wearables',
    price: 199.99,
    rating: 4.2,
    timestamp: '2024-01-15T11:45:00Z',
    source: 'Best Buy',
    competitor: 'FitnessTech',
    tags: ['fitness', 'smartwatch', 'health'],
    metadata: { brand: 'FitTech', model: 'FT-SW200' }
  },
  {
    id: '3',
    title: 'Organic Coffee Beans',
    description: 'Single-origin organic coffee beans from Ethiopia',
    url: 'https://example.com/product/3',
    category: 'Food & Beverage',
    price: 24.99,
    rating: 4.8,
    timestamp: '2024-01-15T09:15:00Z',
    source: 'Local Roaster',
    competitor: 'CoffeeMasters',
    tags: ['organic', 'coffee', 'ethiopian'],
    metadata: { origin: 'Ethiopia', roast: 'Medium' }
  },
  {
    id: '4',
    title: 'Gaming Mechanical Keyboard',
    description: 'RGB backlit mechanical keyboard for gaming',
    url: 'https://example.com/product/4',
    category: 'Electronics',
    price: 149.99,
    rating: 4.6,
    timestamp: '2024-01-15T14:20:00Z',
    source: 'Newegg',
    competitor: 'GameTech',
    tags: ['gaming', 'keyboard', 'mechanical'],
    metadata: { brand: 'GameTech', switches: 'Cherry MX' }
  },
  {
    id: '5',
    title: 'Yoga Mat Premium',
    description: 'Non-slip yoga mat with extra cushioning',
    url: 'https://example.com/product/5',
    category: 'Sports & Outdoors',
    price: 79.99,
    rating: 4.4,
    timestamp: '2024-01-15T16:30:00Z',
    source: 'Target',
    competitor: 'SportsPro',
    tags: ['yoga', 'fitness', 'mat'],
    metadata: { material: 'TPE', thickness: '6mm' }
  },
  {
    id: '6',
    title: 'Bluetooth Speaker',
    description: 'Portable waterproof Bluetooth speaker',
    url: 'https://example.com/product/6',
    category: 'Electronics',
    price: 89.99,
    rating: 4.3,
    timestamp: '2024-01-16T08:15:00Z',
    source: 'Amazon',
    competitor: 'AudioTech',
    tags: ['bluetooth', 'speaker', 'portable'],
    metadata: { brand: 'SoundWave', waterproof: 'IPX7' }
  },
  {
    id: '7',
    title: 'Running Shoes',
    description: 'Lightweight running shoes with advanced cushioning',
    url: 'https://example.com/product/7',
    category: 'Sports & Outdoors',
    price: 129.99,
    rating: 4.7,
    timestamp: '2024-01-16T12:30:00Z',
    source: 'Nike Store',
    competitor: 'SportsPro',
    tags: ['running', 'shoes', 'athletic'],
    metadata: { brand: 'Nike', size: '10.5' }
  },
  {
    id: '8',
    title: 'Protein Powder',
    description: 'Whey protein powder for muscle building',
    url: 'https://example.com/product/8',
    category: 'Food & Beverage',
    price: 49.99,
    rating: 4.1,
    timestamp: '2024-01-16T15:45:00Z',
    source: 'Supplement Store',
    competitor: 'NutriCorp',
    tags: ['protein', 'supplement', 'fitness'],
    metadata: { brand: 'ProFit', flavor: 'Vanilla' }
  }
];

export const mockMetrics: MetricData[] = [
  {
    label: 'Total Items Scraped',
    value: 1247,
    change: 8.2,
    changeType: 'increase',
    icon: 'Database'
  },
  {
    label: 'Average Price',
    value: 189.99,
    change: -3.1,
    changeType: 'decrease',
    icon: 'DollarSign'
  },
  {
    label: 'Data Sources',
    value: 12,
    change: 2,
    changeType: 'increase',
    icon: 'Globe'
  },
  {
    label: 'Success Rate',
    value: 94.7,
    change: 1.3,
    changeType: 'increase',
    icon: 'TrendingUp'
  }
];

export const mockChartData: ChartData[] = [
  { name: 'Jan 1', value: 142, date: '2024-01-01' },
  { name: 'Jan 2', value: 156, date: '2024-01-02' },
  { name: 'Jan 3', value: 189, date: '2024-01-03' },
  { name: 'Jan 4', value: 201, date: '2024-01-04' },
  { name: 'Jan 5', value: 178, date: '2024-01-05' },
  { name: 'Jan 6', value: 234, date: '2024-01-06' },
  { name: 'Jan 7', value: 267, date: '2024-01-07' }
];

export const mockCategoryData: ChartData[] = [
  { name: 'Electronics', value: 452, category: 'Electronics' },
  { name: 'Fashion', value: 328, category: 'Fashion' },
  { name: 'Home & Garden', value: 289, category: 'Home & Garden' },
  { name: 'Sports & Outdoors', value: 178, category: 'Sports & Outdoors' },
  { name: 'Food & Beverage', value: 134, category: 'Food & Beverage' }
];

export const mockCompetitorData: ChartData[] = [
  { name: 'TechCorp', value: 324, competitor: 'TechCorp' },
  { name: 'FitnessTech', value: 289, competitor: 'FitnessTech' },
  { name: 'GameTech', value: 256, competitor: 'GameTech' },
  { name: 'SportsPro', value: 198, competitor: 'SportsPro' },
  { name: 'CoffeeMasters', value: 167, competitor: 'CoffeeMasters' }
];

export const mockCategories: CategoryData[] = [
  {
    id: '1',
    name: 'Electronics',
    description: 'Consumer electronics and tech gadgets',
    itemCount: 452,
    averagePrice: 189.99,
    lastUpdated: '2024-01-16T10:30:00Z',
    color: '#3B82F6'
  },
  {
    id: '2',
    name: 'Sports & Outdoors',
    description: 'Athletic gear and outdoor equipment',
    itemCount: 178,
    averagePrice: 89.99,
    lastUpdated: '2024-01-16T09:15:00Z',
    color: '#10B981'
  },
  {
    id: '3',
    name: 'Food & Beverage',
    description: 'Food products and beverages',
    itemCount: 134,
    averagePrice: 34.99,
    lastUpdated: '2024-01-16T11:45:00Z',
    color: '#F59E0B'
  },
  {
    id: '4',
    name: 'Fashion',
    description: 'Clothing and fashion accessories',
    itemCount: 328,
    averagePrice: 79.99,
    lastUpdated: '2024-01-16T08:20:00Z',
    color: '#EF4444'
  }
];

export const mockCompetitors: CompetitorData[] = [
  {
    id: '1',
    name: 'TechCorp',
    website: 'techcorp.com',
    itemCount: 324,
    averagePrice: 249.99,
    averageRating: 4.3,
    lastUpdated: '2024-01-16T10:30:00Z',
    color: '#3B82F6'
  },
  {
    id: '2',
    name: 'SportsPro',
    website: 'sportspro.com',
    itemCount: 198,
    averagePrice: 104.99,
    averageRating: 4.5,
    lastUpdated: '2024-01-16T09:15:00Z',
    color: '#10B981'
  },
  {
    id: '3',
    name: 'GameTech',
    website: 'gametech.com',
    itemCount: 256,
    averagePrice: 159.99,
    averageRating: 4.4,
    lastUpdated: '2024-01-16T11:45:00Z',
    color: '#8B5CF6'
  },
  {
    id: '4',
    name: 'FitnessTech',
    website: 'fitnesstech.com',
    itemCount: 289,
    averagePrice: 129.99,
    averageRating: 4.2,
    lastUpdated: '2024-01-16T08:20:00Z',
    color: '#F59E0B'
  }
];