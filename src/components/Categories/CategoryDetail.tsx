import React, { useMemo } from 'react';
import { ArrowLeft, Package, DollarSign, Star, TrendingUp } from 'lucide-react';
import { ScrapedData, ChartData } from '../../types';
import { useData } from '../../contexts/DataContext';
import DataTable from '../Data/DataTable';
import LineChartComponent from '../Charts/LineChart';
import BarChartComponent from '../Charts/BarChart';
import RankingComparisonTable from '../Rankings/RankingComparisonTable';

interface CategoryDetailProps {
  categoryName: string;
  onBack: () => void;
}

const CategoryDetail: React.FC<CategoryDetailProps> = ({ categoryName, onBack }) => {
  const { getDataByCategory, getLatestRankingComparison } = useData();

  const categoryData = useMemo(() =>
    getDataByCategory(categoryName),
    [categoryName, getDataByCategory]
  );

  const latestRankingComparison = useMemo(() =>
    getLatestRankingComparison(categoryName),
    [categoryName, getLatestRankingComparison]
  );

  const metrics = useMemo(() => {
    const totalItems = categoryData.length;
    const uniqueAuthors = new Set(categoryData.map(item => item.author).filter(Boolean)).size;
    const newItems = categoryData.filter(item => item.isNew).length;
    const competitors = new Set(categoryData.map(item => item.competitor).filter(Boolean)).size;

    return { totalItems, uniqueAuthors, newItems, competitors };
  }, [categoryData]);

  const authorChartData: ChartData[] = useMemo(() => {
    const authorCounts: Record<string, number> = {};
    categoryData.forEach(item => {
      if (item.author) {
        authorCounts[item.author] = (authorCounts[item.author] || 0) + 1;
      }
    });

    // Get top 10 authors by item count
    return Object.entries(authorCounts)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 10)
      .map(([name, value]) => ({ name, value }));
  }, [categoryData]);

  const competitorChartData: ChartData[] = useMemo(() => {
    const competitorCounts: Record<string, number> = {};
    categoryData.forEach(item => {
      if (item.competitor) {
        competitorCounts[item.competitor] = (competitorCounts[item.competitor] || 0) + 1;
      }
    });

    return Object.entries(competitorCounts).map(([name, value]) => ({ name, value }));
  }, [categoryData]);

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <button
            onClick={onBack}
            className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
          >
            <ArrowLeft size={20} />
          </button>
          <div>
            <h2 className="text-2xl font-bold text-gray-900">{categoryName}</h2>
            <p className="text-gray-600 mt-1">Detailed analytics and data for {categoryName}</p>
          </div>
        </div>
        <div className="flex items-center space-x-2 text-sm text-green-600 bg-green-50 px-3 py-2 rounded-lg">
          <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
          <span>Live Data</span>
        </div>
      </div>

      {/* Metrics Cards */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6">
        <div className="bg-white rounded-lg shadow-md p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Total Items</p>
              <p className="text-2xl font-bold text-gray-900">{metrics.totalItems}</p>
            </div>
            <div className="p-3 bg-blue-50 rounded-lg">
              <Package className="text-blue-600" size={24} />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-md p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Unique Authors</p>
              <p className="text-2xl font-bold text-gray-900">{metrics.uniqueAuthors}</p>
            </div>
            <div className="p-3 bg-green-50 rounded-lg">
              <DollarSign className="text-green-600" size={24} />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-md p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">New Items</p>
              <p className="text-2xl font-bold text-gray-900">{metrics.newItems}</p>
            </div>
            <div className="p-3 bg-yellow-50 rounded-lg">
              <Star className="text-yellow-600" size={24} />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-md p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Competitors</p>
              <p className="text-2xl font-bold text-gray-900">{metrics.competitors}</p>
            </div>
            <div className="p-3 bg-purple-50 rounded-lg">
              <TrendingUp className="text-purple-600" size={24} />
            </div>
          </div>
        </div>
      </div>

      {/* Charts */}
      <div className="grid grid-cols-1 xl:grid-cols-2 gap-6">
        <BarChartComponent
          data={authorChartData}
          title="Top Authors by Item Count"
          dataKey="value"
          height={300}
        />
        <BarChartComponent
          data={competitorChartData}
          title="Items by Competitor"
          dataKey="value"
          height={300}
        />
      </div>

      {/* Ranking Analysis */}
      {latestRankingComparison && (
        <div>
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Ranking Changes</h3>
          <RankingComparisonTable
            comparison={latestRankingComparison}
            onExport={() => {
              // TODO: Implement export functionality
              console.log('Export ranking comparison:', latestRankingComparison);
            }}
          />
        </div>
      )}

      {/* Data Table */}
      <div>
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Category Data</h3>
        <DataTable data={categoryData} />
      </div>
    </div>
  );
};

export default CategoryDetail;