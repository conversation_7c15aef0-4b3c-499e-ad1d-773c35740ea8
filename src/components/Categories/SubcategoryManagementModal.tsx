import React, { useState, useMemo } from 'react';
import { 
  X, 
  Plus, 
  Edit3, 
  Trash2, 
  Save, 
  <PERSON>ert<PERSON>riangle,
  Folder,
  FolderPlus,
  Settings
} from 'lucide-react';
import { useData } from '../../contexts/DataContext';

interface SubcategoryManagementModalProps {
  categoryName: string;
  onClose: () => void;
}

interface Subcategory {
  id: string;
  name: string;
  description?: string;
  itemCount: number;
}

const SubcategoryManagementModal: React.FC<SubcategoryManagementModalProps> = ({
  categoryName,
  onClose
}) => {
  const {
    getDataByCategory,
    categories,
    addSubcategory,
    removeSubcategory,
    updateSubcategory
  } = useData();
  const [editingId, setEditingId] = useState<string | null>(null);
  const [editName, setEditName] = useState('');
  const [editDescription, setEditDescription] = useState('');
  const [showAddForm, setShowAddForm] = useState(false);
  const [newName, setNewName] = useState('');
  const [newDescription, setNewDescription] = useState('');
  const [deleteConfirmId, setDeleteConfirmId] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  // Get category data and find the category
  const categoryData = useMemo(() => getDataByCategory(categoryName), [categoryName, getDataByCategory]);
  const category = useMemo(() => categories.find(cat => cat.name === categoryName), [categories, categoryName]);

  // Extract unique subcategories from the data
  const subcategories = useMemo(() => {
    const subcategoryMap = new Map<string, Subcategory>();
    
    categoryData.forEach(item => {
      // Extract subcategory from tags or description
      const subcategoryName = extractSubcategory(item);
      if (subcategoryName) {
        const existing = subcategoryMap.get(subcategoryName);
        if (existing) {
          existing.itemCount++;
        } else {
          subcategoryMap.set(subcategoryName, {
            id: subcategoryName.toLowerCase().replace(/\s+/g, '-'),
            name: subcategoryName,
            description: `Subcategory of ${categoryName}`,
            itemCount: 1
          });
        }
      }
    });

    return Array.from(subcategoryMap.values()).sort((a, b) => a.name.localeCompare(b.name));
  }, [categoryData, categoryName]);

  // Extract subcategory from item data (this is a simplified implementation)
  const extractSubcategory = (item: any): string | null => {
    // Look for subcategory in tags
    if (item.tags && item.tags.length > 0) {
      // Return the first tag as subcategory for now
      return item.tags[0];
    }
    
    // Could also extract from description or other fields
    return null;
  };

  const handleEdit = (subcategory: Subcategory) => {
    setEditingId(subcategory.id);
    setEditName(subcategory.name);
    setEditDescription(subcategory.description || '');
  };

  const handleSaveEdit = async () => {
    if (!editName.trim() || !editingId) return;

    setIsLoading(true);
    try {
      await updateSubcategory(editingId, {
        name: editName.trim(),
        description: editDescription.trim() || undefined
      });

      setEditingId(null);
      setEditName('');
      setEditDescription('');
    } catch (error) {
      console.error('Failed to update subcategory:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleCancelEdit = () => {
    setEditingId(null);
    setEditName('');
    setEditDescription('');
  };

  const handleAdd = async () => {
    if (!newName.trim() || !category) return;

    setIsLoading(true);
    try {
      await addSubcategory(
        category.id,
        newName.trim(),
        newDescription.trim() || undefined
      );

      setShowAddForm(false);
      setNewName('');
      setNewDescription('');
    } catch (error) {
      console.error('Failed to add subcategory:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleDelete = async (id: string) => {
    setIsLoading(true);
    try {
      await removeSubcategory(id);
      setDeleteConfirmId(null);
    } catch (error) {
      console.error('Failed to delete subcategory:', error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center space-x-3">
            <Settings className="text-gray-600" size={24} />
            <div>
              <h2 className="text-xl font-semibold text-gray-900">Manage Subcategories</h2>
              <p className="text-sm text-gray-600 mt-1">Organize {categoryName} into subcategories</p>
            </div>
          </div>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
          >
            <X size={20} />
          </button>
        </div>

        {/* Content */}
        <div className="p-6 overflow-y-auto max-h-[calc(90vh-140px)]">
          {/* Add New Subcategory */}
          <div className="mb-6">
            {!showAddForm ? (
              <button
                onClick={() => setShowAddForm(true)}
                className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                <Plus size={16} />
                <span>Add Subcategory</span>
              </button>
            ) : (
              <div className="bg-gray-50 p-4 rounded-lg border border-gray-200">
                <h3 className="text-sm font-medium text-gray-900 mb-3">Add New Subcategory</h3>
                <div className="space-y-3">
                  <div>
                    <label className="block text-xs font-medium text-gray-700 mb-1">Name *</label>
                    <input
                      type="text"
                      value={newName}
                      onChange={(e) => setNewName(e.target.value)}
                      placeholder="e.g., Business Templates"
                      className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>
                  <div>
                    <label className="block text-xs font-medium text-gray-700 mb-1">Description</label>
                    <input
                      type="text"
                      value={newDescription}
                      onChange={(e) => setNewDescription(e.target.value)}
                      placeholder="Optional description"
                      className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>
                  <div className="flex items-center space-x-2">
                    <button
                      onClick={handleAdd}
                      disabled={!newName.trim() || isLoading}
                      className="flex items-center space-x-1 px-3 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:opacity-50 text-sm"
                    >
                      <Save size={14} />
                      <span>{isLoading ? 'Adding...' : 'Add'}</span>
                    </button>
                    <button
                      onClick={() => {
                        setShowAddForm(false);
                        setNewName('');
                        setNewDescription('');
                      }}
                      className="px-3 py-2 text-gray-600 hover:text-gray-800 text-sm"
                    >
                      Cancel
                    </button>
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* Subcategories List */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium text-gray-900">
              Current Subcategories ({subcategories.length})
            </h3>
            
            {subcategories.length === 0 ? (
              <div className="text-center py-12 bg-gray-50 rounded-lg">
                <FolderPlus className="mx-auto text-gray-400 mb-4" size={48} />
                <h4 className="text-lg font-medium text-gray-900 mb-2">No subcategories found</h4>
                <p className="text-gray-600 mb-4">
                  Create subcategories to better organize your {categoryName} data
                </p>
                <button
                  onClick={() => setShowAddForm(true)}
                  className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors mx-auto"
                >
                  <Plus size={16} />
                  <span>Add First Subcategory</span>
                </button>
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {subcategories.map((subcategory) => (
                  <div key={subcategory.id} className="bg-white border border-gray-200 rounded-lg p-4">
                    {editingId === subcategory.id ? (
                      <div className="space-y-3">
                        <input
                          type="text"
                          value={editName}
                          onChange={(e) => setEditName(e.target.value)}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                        />
                        <input
                          type="text"
                          value={editDescription}
                          onChange={(e) => setEditDescription(e.target.value)}
                          placeholder="Description"
                          className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                        />
                        <div className="flex items-center space-x-2">
                          <button
                            onClick={handleSaveEdit}
                            disabled={!editName.trim() || isLoading}
                            className="flex items-center space-x-1 px-3 py-1 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:opacity-50 text-sm"
                          >
                            <Save size={12} />
                            <span>Save</span>
                          </button>
                          <button
                            onClick={handleCancelEdit}
                            className="px-3 py-1 text-gray-600 hover:text-gray-800 text-sm"
                          >
                            Cancel
                          </button>
                        </div>
                      </div>
                    ) : (
                      <div>
                        <div className="flex items-start justify-between mb-2">
                          <div className="flex items-center space-x-2">
                            <Folder className="text-blue-500 flex-shrink-0" size={16} />
                            <h4 className="font-medium text-gray-900 truncate">{subcategory.name}</h4>
                          </div>
                          <div className="flex items-center space-x-1">
                            <button
                              onClick={() => handleEdit(subcategory)}
                              className="p-1 text-gray-400 hover:text-blue-600 transition-colors"
                              title="Edit subcategory"
                            >
                              <Edit3 size={14} />
                            </button>
                            <button
                              onClick={() => setDeleteConfirmId(subcategory.id)}
                              className="p-1 text-gray-400 hover:text-red-600 transition-colors"
                              title="Delete subcategory"
                            >
                              <Trash2 size={14} />
                            </button>
                          </div>
                        </div>
                        <p className="text-sm text-gray-600 mb-2 truncate">{subcategory.description}</p>
                        <div className="flex items-center justify-between text-xs text-gray-500">
                          <span>{subcategory.itemCount} items</span>
                          <span>ID: {subcategory.id}</span>
                        </div>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>

        {/* Delete Confirmation Modal */}
        {deleteConfirmId && (
          <div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center">
            <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
              <div className="flex items-center space-x-3 mb-4">
                <AlertTriangle className="text-red-500" size={24} />
                <h3 className="text-lg font-semibold text-gray-900">Confirm Deletion</h3>
              </div>
              <p className="text-gray-600 mb-6">
                Are you sure you want to delete this subcategory? This action cannot be undone and may affect data organization.
              </p>
              <div className="flex space-x-3">
                <button
                  onClick={() => setDeleteConfirmId(null)}
                  className="flex-1 px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
                >
                  Cancel
                </button>
                <button
                  onClick={() => handleDelete(deleteConfirmId)}
                  disabled={isLoading}
                  className="flex-1 px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 disabled:opacity-50"
                >
                  {isLoading ? 'Deleting...' : 'Delete'}
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default SubcategoryManagementModal;
