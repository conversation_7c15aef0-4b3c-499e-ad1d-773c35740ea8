import React, { useState } from 'react';
import { 
  FileText, 
  Database, 
  Settings as SettingsIcon, 
  Bell, 
  Shield, 
  Download,
  Trash2,
  HardDrive
} from 'lucide-react';
import FileManagement from './FileManagement';

interface SettingsProps {
  onClose?: () => void;
  initialSection?: SettingsSection;
}

type SettingsSection = 'general' | 'files' | 'notifications' | 'security' | 'storage' | 'export';

const Settings: React.FC<SettingsProps> = ({ onClose, initialSection = 'general' }) => {
  const [activeSection, setActiveSection] = useState<SettingsSection>(initialSection);

  const settingsSections = [
    { id: 'general' as SettingsSection, label: 'General', icon: SettingsIcon },
    { id: 'files' as SettingsSection, label: 'File Management', icon: FileText },
    { id: 'storage' as SettingsSection, label: 'Storage', icon: HardDrive },
    { id: 'export' as SettingsSection, label: 'Export & Backup', icon: Download },
    { id: 'notifications' as SettingsSection, label: 'Notifications', icon: Bell },
    { id: 'security' as SettingsSection, label: 'Security', icon: Shield }
  ];

  const renderSectionContent = () => {
    switch (activeSection) {
      case 'files':
        return <FileManagement />;
      
      case 'general':
        return (
          <div className="space-y-6">
            <h3 className="text-lg font-semibold text-gray-900">General Settings</h3>
            <div className="bg-white rounded-lg border border-gray-200 p-6">
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Dashboard Theme
                  </label>
                  <select className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500">
                    <option>Light</option>
                    <option>Dark</option>
                    <option>Auto</option>
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Default Items Per Page
                  </label>
                  <select className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500">
                    <option>10</option>
                    <option>25</option>
                    <option>50</option>
                    <option>100</option>
                  </select>
                </div>
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="autoRefresh"
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                  <label htmlFor="autoRefresh" className="ml-2 text-sm text-gray-700">
                    Auto-refresh data every 5 minutes
                  </label>
                </div>
              </div>
            </div>
          </div>
        );

      case 'storage':
        return (
          <div className="space-y-6">
            <h3 className="text-lg font-semibold text-gray-900">Storage Management</h3>
            <div className="bg-white rounded-lg border border-gray-200 p-6">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <h4 className="font-medium text-gray-900">Local Storage Usage</h4>
                    <p className="text-sm text-gray-600">Data stored in your browser</p>
                  </div>
                  <div className="text-right">
                    <div className="text-lg font-semibold text-gray-900">~2.3 MB</div>
                    <div className="text-sm text-gray-600">of 10 MB limit</div>
                  </div>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div className="bg-blue-600 h-2 rounded-full" style={{ width: '23%' }}></div>
                </div>
                <div className="pt-4 border-t border-gray-200">
                  <button className="flex items-center space-x-2 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700">
                    <Trash2 size={16} />
                    <span>Clear All Data</span>
                  </button>
                  <p className="text-xs text-gray-500 mt-2">
                    This will permanently delete all imported data and settings
                  </p>
                </div>
              </div>
            </div>
          </div>
        );

      case 'export':
        return (
          <div className="space-y-6">
            <h3 className="text-lg font-semibold text-gray-900">Export & Backup</h3>
            <div className="bg-white rounded-lg border border-gray-200 p-6">
              <div className="space-y-4">
                <div>
                  <h4 className="font-medium text-gray-900 mb-2">Export Data</h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <button className="flex items-center justify-center space-x-2 px-4 py-3 border border-gray-300 rounded-lg hover:bg-gray-50">
                      <Download size={16} />
                      <span>Export All Data (CSV)</span>
                    </button>
                    <button className="flex items-center justify-center space-x-2 px-4 py-3 border border-gray-300 rounded-lg hover:bg-gray-50">
                      <Download size={16} />
                      <span>Export Rankings (JSON)</span>
                    </button>
                  </div>
                </div>
                <div className="pt-4 border-t border-gray-200">
                  <h4 className="font-medium text-gray-900 mb-2">Backup Settings</h4>
                  <div className="flex items-center">
                    <input
                      type="checkbox"
                      id="autoBackup"
                      className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    />
                    <label htmlFor="autoBackup" className="ml-2 text-sm text-gray-700">
                      Automatically backup data weekly
                    </label>
                  </div>
                </div>
              </div>
            </div>
          </div>
        );

      case 'notifications':
        return (
          <div className="space-y-6">
            <h3 className="text-lg font-semibold text-gray-900">Notification Settings</h3>
            <div className="bg-white rounded-lg border border-gray-200 p-6">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <h4 className="font-medium text-gray-900">Import Notifications</h4>
                    <p className="text-sm text-gray-600">Get notified when CSV imports complete</p>
                  </div>
                  <input
                    type="checkbox"
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    defaultChecked
                  />
                </div>
                <div className="flex items-center justify-between">
                  <div>
                    <h4 className="font-medium text-gray-900">Ranking Changes</h4>
                    <p className="text-sm text-gray-600">Alert when significant ranking changes occur</p>
                  </div>
                  <input
                    type="checkbox"
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                </div>
                <div className="flex items-center justify-between">
                  <div>
                    <h4 className="font-medium text-gray-900">Storage Warnings</h4>
                    <p className="text-sm text-gray-600">Warn when approaching storage limits</p>
                  </div>
                  <input
                    type="checkbox"
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    defaultChecked
                  />
                </div>
              </div>
            </div>
          </div>
        );

      case 'security':
        return (
          <div className="space-y-6">
            <h3 className="text-lg font-semibold text-gray-900">Security Settings</h3>
            <div className="bg-white rounded-lg border border-gray-200 p-6">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <h4 className="font-medium text-gray-900">Data Encryption</h4>
                    <p className="text-sm text-gray-600">Encrypt sensitive data in local storage</p>
                  </div>
                  <input
                    type="checkbox"
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    defaultChecked
                  />
                </div>
                <div className="flex items-center justify-between">
                  <div>
                    <h4 className="font-medium text-gray-900">Session Timeout</h4>
                    <p className="text-sm text-gray-600">Auto-logout after inactivity</p>
                  </div>
                  <select className="px-3 py-1 border border-gray-300 rounded-md text-sm">
                    <option>Never</option>
                    <option>30 minutes</option>
                    <option>1 hour</option>
                    <option>4 hours</option>
                  </select>
                </div>
              </div>
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-xl font-semibold text-gray-900">Settings</h2>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* Settings Navigation */}
        <div className="lg:col-span-1">
          <div className="bg-white rounded-lg border border-gray-200 p-4">
            <nav className="space-y-1">
              {settingsSections.map((section) => {
                const Icon = section.icon;
                return (
                  <button
                    key={section.id}
                    onClick={() => setActiveSection(section.id)}
                    className={`w-full flex items-center space-x-3 px-3 py-2 rounded-lg text-left text-sm transition-colors ${
                      activeSection === section.id
                        ? 'bg-blue-50 text-blue-700 border-r-2 border-blue-700'
                        : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
                    }`}
                  >
                    <Icon size={16} />
                    <span>{section.label}</span>
                  </button>
                );
              })}
            </nav>
          </div>
        </div>

        {/* Settings Content */}
        <div className="lg:col-span-3">
          {renderSectionContent()}
        </div>
      </div>
    </div>
  );
};

export default Settings;
