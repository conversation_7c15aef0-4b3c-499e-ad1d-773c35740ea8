import React, { useState } from 'react';
import {
  FileText,
  Edit3,
  Trash2,
  Eye,
  Search,
  Filter,
  CheckCircle,
  Clock,
  Database,
  AlertTriangle,
  Save,
  X,
  Files,
  Camera,
  BarChart3,
  Table
} from 'lucide-react';
import { DataSource } from '../../types';
import { useData } from '../../contexts/DataContext';
import DataSourceDetail from '../Data/DataSourceDetail';

const FileManagement: React.FC = () => {
  const { dataSources, categorySnapshots, rankingComparisons, removeDataSource, updateDataSource } = useData();
  const [selectedSources, setSelectedSources] = useState<string[]>([]);
  const [filterCategory, setFilterCategory] = useState<string>('all');
  const [searchTerm, setSearchTerm] = useState('');
  const [showDeleteConfirm, setShowDeleteConfirm] = useState<string | null>(null);
  const [isDeleting, setIsDeleting] = useState(false);
  const [selectedSourceDetail, setSelectedSourceDetail] = useState<DataSource | null>(null);
  const [editingSource, setEditingSource] = useState<string | null>(null);
  const [editName, setEditName] = useState('');
  const [isRenaming, setIsRenaming] = useState(false);

  // Get unique categories from data sources
  const categories = Array.from(new Set(dataSources.map(ds => ds.category).filter(Boolean)));

  // Filter data sources
  const filteredSources = dataSources.filter(source => {
    const matchesCategory = filterCategory === 'all' || source.category === filterCategory;
    const matchesSearch = searchTerm === '' || 
      source.filename.toLowerCase().includes(searchTerm.toLowerCase()) ||
      source.name.toLowerCase().includes(searchTerm.toLowerCase());
    return matchesCategory && matchesSearch;
  });

  // Get related snapshots and comparisons for a data source
  const getRelatedData = (source: DataSource) => {
    const relatedSnapshots = categorySnapshots.filter(snapshot => 
      snapshot.filename === source.filename
    );
    const relatedComparisons = rankingComparisons.filter(comparison =>
      comparison.currentSnapshot.filename === source.filename ||
      comparison.previousSnapshot?.filename === source.filename
    );
    return { snapshots: relatedSnapshots, comparisons: relatedComparisons };
  };

  // Handle individual file deletion
  const handleDeleteSource = async (sourceId: string) => {
    setIsDeleting(true);
    try {
      const success = await removeDataSource(sourceId);
      if (success) {
        setShowDeleteConfirm(null);
      }
    } catch (error) {
      console.error('Failed to delete data source:', error);
    } finally {
      setIsDeleting(false);
    }
  };

  // Handle bulk deletion
  const handleBulkDelete = async () => {
    setIsDeleting(true);
    try {
      for (const sourceId of selectedSources) {
        await removeDataSource(sourceId);
      }
      setSelectedSources([]);
    } catch (error) {
      console.error('Failed to delete data sources:', error);
    } finally {
      setIsDeleting(false);
    }
  };

  // Handle rename functionality
  const startRename = (source: DataSource) => {
    setEditingSource(source.id);
    setEditName(source.name);
  };

  const cancelRename = () => {
    setEditingSource(null);
    setEditName('');
  };

  const saveRename = async (sourceId: string) => {
    if (!editName.trim()) return;
    
    setIsRenaming(true);
    try {
      const success = await updateDataSource(sourceId, { name: editName.trim() });
      if (success) {
        setEditingSource(null);
        setEditName('');
      }
    } catch (error) {
      console.error('Failed to rename data source:', error);
    } finally {
      setIsRenaming(false);
    }
  };

  // Toggle source selection
  const toggleSourceSelection = (sourceId: string) => {
    setSelectedSources(prev => 
      prev.includes(sourceId) 
        ? prev.filter(id => id !== sourceId)
        : [...prev, sourceId]
    );
  };

  // Format file size
  const formatFileSize = (rowCount: number) => {
    const estimatedSize = rowCount * 100; // Rough estimate
    if (estimatedSize < 1024) return `${estimatedSize} B`;
    if (estimatedSize < 1024 * 1024) return `${(estimatedSize / 1024).toFixed(1)} KB`;
    return `${(estimatedSize / (1024 * 1024)).toFixed(1)} MB`;
  };

  // Format date
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // Get file type indicator
  const getFileTypeIndicator = (source: DataSource) => {
    const { snapshots, comparisons } = getRelatedData(source);
    const isRankingFile = snapshots.length > 0;
    const hasComparisons = comparisons.length > 0;

    if (isRankingFile && hasComparisons) {
      return { icon: CheckCircle, color: 'text-green-500', label: 'Ranking Analysis' };
    } else if (isRankingFile) {
      return { icon: Clock, color: 'text-yellow-500', label: 'Ranking Snapshot' };
    } else {
      return { icon: Database, color: 'text-blue-500', label: 'Standard Data' };
    }
  };

  // Show detail view if a source is selected
  if (selectedSourceDetail) {
    return (
      <div className="space-y-6">
        <DataSourceDetail
          source={selectedSourceDetail}
          onBack={() => setSelectedSourceDetail(null)}
          onDelete={handleDeleteSource}
        />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-semibold text-gray-900">File Management</h3>
        <p className="text-sm text-gray-600 mt-1">
          Manage your uploaded CSV files, rename them, and control your data sources
        </p>
      </div>

      {/* Summary Stats */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
        <div className="bg-white rounded-lg shadow-md p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Total Files</p>
              <p className="text-2xl font-bold text-gray-900">{dataSources.length}</p>
            </div>
            <div className="p-3 bg-blue-50 rounded-lg">
              <Files className="text-blue-600" size={24} />
            </div>
          </div>
        </div>
        <div className="bg-white rounded-lg shadow-md p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Ranking Snapshots</p>
              <p className="text-2xl font-bold text-gray-900">{categorySnapshots.length}</p>
            </div>
            <div className="p-3 bg-green-50 rounded-lg">
              <Camera className="text-green-600" size={24} />
            </div>
          </div>
        </div>
        <div className="bg-white rounded-lg shadow-md p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Ranking Comparisons</p>
              <p className="text-2xl font-bold text-gray-900">{rankingComparisons.length}</p>
            </div>
            <div className="p-3 bg-purple-50 rounded-lg">
              <BarChart3 className="text-purple-600" size={24} />
            </div>
          </div>
        </div>
        <div className="bg-white rounded-lg shadow-md p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Total Rows</p>
              <p className="text-2xl font-bold text-gray-900">
                {dataSources.reduce((sum, ds) => sum + ds.rowCount, 0).toLocaleString()}
              </p>
            </div>
            <div className="p-3 bg-gray-50 rounded-lg">
              <Table className="text-gray-600" size={24} />
            </div>
          </div>
        </div>
      </div>

      {/* Filters and Search */}
      <div className="bg-white rounded-lg border border-gray-200 p-4">
        <div className="flex flex-col space-y-4 md:flex-row md:space-y-0 md:space-x-4 md:items-center">
          <div className="flex items-center space-x-2">
            <Search size={16} className="text-gray-500" />
            <input
              type="text"
              placeholder="Search files..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
          
          <div className="flex items-center space-x-2">
            <Filter size={16} className="text-gray-500" />
            <select
              value={filterCategory}
              onChange={(e) => setFilterCategory(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="all">All Categories</option>
              {categories.map(category => (
                <option key={category} value={category}>{category}</option>
              ))}
            </select>
          </div>

          {selectedSources.length > 0 && (
            <button
              onClick={handleBulkDelete}
              disabled={isDeleting}
              className="flex items-center space-x-2 px-3 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 disabled:opacity-50 text-sm"
            >
              <Trash2 size={14} />
              <span>Delete Selected ({selectedSources.length})</span>
            </button>
          )}
        </div>
      </div>

      {/* File List */}
      <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-4 py-3 text-left">
                  <input
                    type="checkbox"
                    checked={selectedSources.length === filteredSources.length && filteredSources.length > 0}
                    onChange={(e) => {
                      if (e.target.checked) {
                        setSelectedSources(filteredSources.map(s => s.id));
                      } else {
                        setSelectedSources([]);
                      }
                    }}
                    className="rounded border-gray-300"
                  />
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">File Name</th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Type</th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Category</th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Rows</th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Upload Date</th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Actions</th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredSources.map((source) => {
                const { snapshots, comparisons } = getRelatedData(source);
                const typeIndicator = getFileTypeIndicator(source);
                const TypeIcon = typeIndicator.icon;
                const isEditing = editingSource === source.id;

                return (
                  <tr key={source.id} className="hover:bg-gray-50">
                    <td className="px-4 py-3">
                      <input
                        type="checkbox"
                        checked={selectedSources.includes(source.id)}
                        onChange={() => toggleSourceSelection(source.id)}
                        className="rounded border-gray-300"
                      />
                    </td>
                    <td className="px-4 py-3">
                      <div className="flex items-center space-x-3">
                        <FileText size={16} className="text-gray-400" />
                        <div className="min-w-0 flex-1">
                          {isEditing ? (
                            <div className="flex items-center space-x-2">
                              <input
                                type="text"
                                value={editName}
                                onChange={(e) => setEditName(e.target.value)}
                                className="flex-1 px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
                                onKeyPress={(e) => {
                                  if (e.key === 'Enter') {
                                    saveRename(source.id);
                                  } else if (e.key === 'Escape') {
                                    cancelRename();
                                  }
                                }}
                                autoFocus
                              />
                              <button
                                onClick={() => saveRename(source.id)}
                                disabled={isRenaming}
                                className="p-1 text-green-600 hover:text-green-800"
                              >
                                <Save size={14} />
                              </button>
                              <button
                                onClick={cancelRename}
                                className="p-1 text-gray-600 hover:text-gray-800"
                              >
                                <X size={14} />
                              </button>
                            </div>
                          ) : (
                            <div>
                              <div className="text-sm font-medium text-gray-900 truncate">{source.name}</div>
                              <div className="text-xs text-gray-500 truncate">{source.filename}</div>
                            </div>
                          )}
                        </div>
                      </div>
                    </td>
                    <td className="px-4 py-3">
                      <div className="flex items-center space-x-2">
                        <TypeIcon size={16} className={typeIndicator.color} />
                        <span className="text-xs text-gray-600">{typeIndicator.label}</span>
                      </div>
                    </td>
                    <td className="px-4 py-3">
                      <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800">
                        {source.category || 'Unknown'}
                      </span>
                    </td>
                    <td className="px-4 py-3">
                      <div className="text-sm text-gray-900">{source.rowCount.toLocaleString()}</div>
                      <div className="text-xs text-gray-500">{formatFileSize(source.rowCount)}</div>
                    </td>
                    <td className="px-4 py-3">
                      <div className="text-sm text-gray-900">{formatDate(source.uploadDate)}</div>
                    </td>
                    <td className="px-4 py-3">
                      <div className="flex items-center space-x-2">
                        <button
                          onClick={() => setSelectedSourceDetail(source)}
                          className="text-blue-600 hover:text-blue-800"
                          title="View Details"
                        >
                          <Eye size={16} />
                        </button>
                        <button
                          onClick={() => startRename(source)}
                          className="text-green-600 hover:text-green-800"
                          title="Rename File"
                        >
                          <Edit3 size={16} />
                        </button>
                        <button
                          onClick={() => setShowDeleteConfirm(source.id)}
                          className="text-red-600 hover:text-red-800"
                          title="Delete File"
                        >
                          <Trash2 size={16} />
                        </button>
                      </div>
                    </td>
                  </tr>
                );
              })}
            </tbody>
          </table>

          {filteredSources.length === 0 && (
            <div className="text-center py-12">
              <FileText className="mx-auto text-gray-400 mb-4" size={48} />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No files found</h3>
              <p className="text-gray-600">
                {searchTerm || filterCategory !== 'all' 
                  ? 'Try adjusting your search or filter criteria.'
                  : 'Upload some CSV files to get started.'
                }
              </p>
            </div>
          )}
        </div>
      </div>

      {/* Delete Confirmation Modal */}
      {showDeleteConfirm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
            <div className="flex items-center space-x-3 mb-4">
              <AlertTriangle className="text-red-500" size={24} />
              <h3 className="text-lg font-semibold text-gray-900">Confirm Deletion</h3>
            </div>
            <p className="text-gray-600 mb-6">
              Are you sure you want to delete this file and all its associated data? This action cannot be undone.
            </p>
            <div className="flex space-x-3">
              <button
                onClick={() => setShowDeleteConfirm(null)}
                className="flex-1 px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
              >
                Cancel
              </button>
              <button
                onClick={() => handleDeleteSource(showDeleteConfirm)}
                disabled={isDeleting}
                className="flex-1 px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 disabled:opacity-50"
              >
                {isDeleting ? 'Deleting...' : 'Delete'}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default FileManagement;
