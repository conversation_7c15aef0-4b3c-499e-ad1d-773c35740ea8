import React from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CartesianGrid, <PERSON><PERSON><PERSON>, Legend, ResponsiveContainer } from 'recharts';
import { ChartData } from '../../types';

interface BarChartComponentProps {
  data: ChartData[];
  title: string;
  dataKey: string;
  height?: number;
}

const BarChartComponent: React.FC<BarChartComponentProps> = ({ 
  data, 
  title, 
  dataKey,
  height = 300 
}) => {
  return (
    <div className="bg-white rounded-lg shadow-md p-6">
      <h3 className="text-lg font-semibold text-gray-900 mb-4">{title}</h3>
      <ResponsiveContainer width="100%" height={height}>
        <BarChart data={data}>
          <CartesianGrid strokeDasharray="3 3" />
          <XAxis dataKey="name" />
          <YAxis />
          <Tooltip />
          <Legend />
          <Bar 
            dataKey={dataKey} 
            fill="#14B8A6"
            radius={[4, 4, 0, 0]}
          />
        </Bar<PERSON><PERSON>>
      </ResponsiveContainer>
    </div>
  );
};

export default BarChartComponent;