import React from 'react';
import { TrendingUp, TrendingDown, Minus } from 'lucide-react';
import * as Icons from 'lucide-react';
import { MetricData } from '../../types';

interface MetricsCardProps {
  metric: MetricData;
}

const MetricsCard: React.FC<MetricsCardProps> = ({ metric }) => {
  const IconComponent = Icons[metric.icon as keyof typeof Icons] as React.ComponentType<any>;
  
  const getTrendIcon = () => {
    switch (metric.changeType) {
      case 'increase':
        return <TrendingUp size={16} className="text-green-600" />;
      case 'decrease':
        return <TrendingDown size={16} className="text-red-600" />;
      default:
        return <Minus size={16} className="text-gray-600" />;
    }
  };

  const getTrendColor = () => {
    switch (metric.changeType) {
      case 'increase':
        return 'text-green-600';
      case 'decrease':
        return 'text-red-600';
      default:
        return 'text-gray-600';
    }
  };

  return (
    <div className="bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow">
      <div className="flex items-center justify-between mb-4">
        <div className="p-2 bg-blue-50 rounded-lg">
          {IconComponent && <IconComponent size={24} className="text-blue-600" />}
        </div>
        <div className="flex items-center space-x-1">
          {getTrendIcon()}
          <span className={`text-sm font-medium ${getTrendColor()}`}>
            {Math.abs(metric.change)}%
          </span>
        </div>
      </div>
      
      <div>
        <h3 className="text-2xl font-bold text-gray-900 mb-1">
          {typeof metric.value === 'number' && metric.label.includes('Price') 
            ? `$${metric.value.toFixed(2)}`
            : typeof metric.value === 'number' && metric.label.includes('Rate')
            ? `${metric.value.toFixed(1)}%`
            : metric.value.toLocaleString()
          }
        </h3>
        <p className="text-gray-600 text-sm">{metric.label}</p>
      </div>
    </div>
  );
};

export default MetricsCard;