import React, { useState } from 'react';
import { Download, FileText, FileSpreadsheet, FileImage } from 'lucide-react';
import { ScrapedData, ExportOptions } from '../../types';
import { handleExport } from '../../utils/exportUtils';

interface ExportPanelProps {
  data: ScrapedData[];
  filteredData: ScrapedData[];
}

const ExportPanel: React.FC<ExportPanelProps> = ({ data, filteredData }) => {
  const [exportOptions, setExportOptions] = useState<ExportOptions>({
    format: 'csv',
    includeFilters: false
  });

  const handleExportClick = () => {
    const dataToExport = exportOptions.includeFilters ? filteredData : data;
    handleExport(dataToExport, exportOptions);
  };

  const getIcon = (format: string) => {
    switch (format) {
      case 'csv':
        return <FileText size={20} />;
      case 'excel':
        return <FileSpreadsheet size={20} />;
      case 'pdf':
        return <FileImage size={20} />;
      default:
        return <Download size={20} />;
    }
  };

  return (
    <div className="bg-white rounded-lg shadow-md p-6">
      <h3 className="text-lg font-semibold text-gray-900 mb-6">Export Data</h3>
      
      <div className="space-y-6">
        {/* Format Selection */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-3">
            Export Format
          </label>
          <div className="grid grid-cols-3 gap-3">
            {[
              { value: 'csv', label: 'CSV' },
              { value: 'excel', label: 'Excel' },
              { value: 'pdf', label: 'PDF' }
            ].map((format) => (
              <button
                key={format.value}
                onClick={() => setExportOptions({ ...exportOptions, format: format.value as any })}
                className={`p-3 border rounded-lg flex flex-col items-center space-y-2 transition-colors ${
                  exportOptions.format === format.value
                    ? 'border-blue-500 bg-blue-50 text-blue-700'
                    : 'border-gray-300 hover:border-gray-400'
                }`}
              >
                {getIcon(format.value)}
                <span className="text-sm font-medium">{format.label}</span>
              </button>
            ))}
          </div>
        </div>

        {/* Options */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-3">
            Export Options
          </label>
          <div className="space-y-3">
            <label className="flex items-center">
              <input
                type="checkbox"
                checked={exportOptions.includeFilters}
                onChange={(e) => setExportOptions({ 
                  ...exportOptions, 
                  includeFilters: e.target.checked 
                })}
                className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              />
              <span className="ml-2 text-sm text-gray-700">
                Apply current filters ({filteredData.length} items)
              </span>
            </label>
          </div>
        </div>

        {/* Data Summary */}
        <div className="bg-gray-50 rounded-lg p-4">
          <h4 className="text-sm font-medium text-gray-900 mb-2">Export Summary</h4>
          <div className="space-y-1 text-sm text-gray-600">
            <p>Total items: {data.length}</p>
            <p>Filtered items: {filteredData.length}</p>
            <p>Export format: {exportOptions.format.toUpperCase()}</p>
            <p>
              Items to export: {exportOptions.includeFilters ? filteredData.length : data.length}
            </p>
          </div>
        </div>

        {/* Export Button */}
        <button
          onClick={handleExportClick}
          className="w-full bg-blue-600 text-white py-3 px-4 rounded-lg hover:bg-blue-700 transition-colors flex items-center justify-center space-x-2"
        >
          <Download size={20} />
          <span>Export Data</span>
        </button>
      </div>
    </div>
  );
};

export default ExportPanel;