import React from 'react';
import { Users, TrendingUp, Package, DollarSign, Star, Globe } from 'lucide-react';
import { CompetitorData } from '../../types';
import { mockCompetitors, mockCompetitorData } from '../../data/mockData';
import PieChartComponent from '../Charts/PieChart';
import BarChartComponent from '../Charts/BarChart';

const CompetitorOverview: React.FC = () => {
  const totalItems = mockCompetitors.reduce((sum, comp) => sum + comp.itemCount, 0);
  const avgPrice = mockCompetitors.reduce((sum, comp) => sum + comp.averagePrice, 0) / mockCompetitors.length;
  const avgRating = mockCompetitors.reduce((sum, comp) => sum + comp.averageRating, 0) / mockCompetitors.length;

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Competitors Overview</h2>
          <p className="text-gray-600 mt-1">Comprehensive analysis of competitor data</p>
        </div>
        <div className="flex items-center space-x-2 text-sm text-green-600 bg-green-50 px-3 py-2 rounded-lg">
          <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
          <span>Live Data</span>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-white rounded-lg shadow-md p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Total Competitors</p>
              <p className="text-2xl font-bold text-gray-900">{mockCompetitors.length}</p>
            </div>
            <div className="p-3 bg-blue-50 rounded-lg">
              <Users className="text-blue-600" size={24} />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-md p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Total Items</p>
              <p className="text-2xl font-bold text-gray-900">{totalItems.toLocaleString()}</p>
            </div>
            <div className="p-3 bg-green-50 rounded-lg">
              <Package className="text-green-600" size={24} />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-md p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Avg Price</p>
              <p className="text-2xl font-bold text-gray-900">${avgPrice.toFixed(2)}</p>
            </div>
            <div className="p-3 bg-yellow-50 rounded-lg">
              <DollarSign className="text-yellow-600" size={24} />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-md p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Avg Rating</p>
              <p className="text-2xl font-bold text-gray-900">{avgRating.toFixed(1)}</p>
            </div>
            <div className="p-3 bg-purple-50 rounded-lg">
              <Star className="text-purple-600" size={24} />
            </div>
          </div>
        </div>
      </div>

      {/* Competitor Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {mockCompetitors.map((competitor) => (
          <div key={competitor.id} className="bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow">
            <div className="flex items-center justify-between mb-4">
              <div 
                className="w-4 h-4 rounded-full"
                style={{ backgroundColor: competitor.color }}
              ></div>
              <span className="text-xs text-gray-500">
                {new Date(competitor.lastUpdated).toLocaleDateString()}
              </span>
            </div>
            
            <h3 className="text-lg font-semibold text-gray-900 mb-2">{competitor.name}</h3>
            <div className="flex items-center text-sm text-gray-600 mb-4">
              <Globe size={14} className="mr-1" />
              <span>{competitor.website}</span>
            </div>
            
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span className="text-gray-600">Items:</span>
                <span className="font-medium">{competitor.itemCount}</span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-gray-600">Avg Price:</span>
                <span className="font-medium">${competitor.averagePrice}</span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-gray-600">Avg Rating:</span>
                <span className="font-medium">{competitor.averageRating}</span>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <PieChartComponent 
          data={mockCompetitorData} 
          title="Market Share by Items"
          dataKey="value"
          height={300}
        />
        <BarChartComponent 
          data={mockCompetitorData} 
          title="Competitor Performance"
          dataKey="value"
          height={300}
        />
      </div>

      {/* Comparison Table */}
      <div className="bg-white rounded-lg shadow-md overflow-hidden">
        <div className="p-6 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900">Competitor Comparison</h3>
        </div>
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Competitor
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Items
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Avg Price
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Avg Rating
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Last Updated
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {mockCompetitors.map((competitor) => (
                <tr key={competitor.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <div 
                        className="w-3 h-3 rounded-full mr-3"
                        style={{ backgroundColor: competitor.color }}
                      ></div>
                      <div>
                        <div className="text-sm font-medium text-gray-900">{competitor.name}</div>
                        <div className="text-sm text-gray-500">{competitor.website}</div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {competitor.itemCount}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    ${competitor.averagePrice}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <Star size={16} className="text-yellow-400 fill-current mr-1" />
                      <span className="text-sm text-gray-900">{competitor.averageRating}</span>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {new Date(competitor.lastUpdated).toLocaleDateString()}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};

export default CompetitorOverview;