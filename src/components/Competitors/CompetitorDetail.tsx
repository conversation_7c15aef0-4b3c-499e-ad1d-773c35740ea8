import React, { useMemo } from 'react';
import { ArrowLeft, Package, DollarSign, Star, TrendingUp, Globe } from 'lucide-react';
import { ScrapedData, ChartData } from '../../types';
import { mockScrapedData } from '../../data/mockData';
import DataTable from '../Data/DataTable';
import BarChartComponent from '../Charts/BarChart';
import PieChartComponent from '../Charts/PieChart';

interface CompetitorDetailProps {
  competitorName: string;
  onBack: () => void;
}

const CompetitorDetail: React.FC<CompetitorDetailProps> = ({ competitorName, onBack }) => {
  const competitorData = useMemo(() => 
    mockScrapedData.filter(item => item.competitor === competitorName),
    [competitorName]
  );

  const metrics = useMemo(() => {
    const totalItems = competitorData.length;
    const avgPrice = competitorData.reduce((sum, item) => sum + (item.price || 0), 0) / totalItems;
    const avgRating = competitorData.reduce((sum, item) => sum + (item.rating || 0), 0) / totalItems;
    const categories = new Set(competitorData.map(item => item.category)).size;

    return { totalItems, avgPrice, avgRating, categories };
  }, [competitorData]);

  const categoryChartData: ChartData[] = useMemo(() => {
    const categoryCounts: Record<string, number> = {};
    competitorData.forEach(item => {
      categoryCounts[item.category] = (categoryCounts[item.category] || 0) + 1;
    });

    return Object.entries(categoryCounts).map(([name, value]) => ({ name, value }));
  }, [competitorData]);

  const priceChartData: ChartData[] = useMemo(() => {
    const priceRanges = {
      '$0-50': 0,
      '$51-100': 0,
      '$101-200': 0,
      '$201+': 0
    };

    competitorData.forEach(item => {
      if (!item.price) return;
      if (item.price <= 50) priceRanges['$0-50']++;
      else if (item.price <= 100) priceRanges['$51-100']++;
      else if (item.price <= 200) priceRanges['$101-200']++;
      else priceRanges['$201+']++;
    });

    return Object.entries(priceRanges).map(([name, value]) => ({ name, value }));
  }, [competitorData]);

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <button
            onClick={onBack}
            className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
          >
            <ArrowLeft size={20} />
          </button>
          <div>
            <h2 className="text-2xl font-bold text-gray-900">{competitorName}</h2>
            <p className="text-gray-600 mt-1">Detailed analytics and data for {competitorName}</p>
          </div>
        </div>
        <div className="flex items-center space-x-2 text-sm text-green-600 bg-green-50 px-3 py-2 rounded-lg">
          <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
          <span>Live Data</span>
        </div>
      </div>

      {/* Metrics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-white rounded-lg shadow-md p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Total Items</p>
              <p className="text-2xl font-bold text-gray-900">{metrics.totalItems}</p>
            </div>
            <div className="p-3 bg-blue-50 rounded-lg">
              <Package className="text-blue-600" size={24} />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-md p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Avg Price</p>
              <p className="text-2xl font-bold text-gray-900">${metrics.avgPrice.toFixed(2)}</p>
            </div>
            <div className="p-3 bg-green-50 rounded-lg">
              <DollarSign className="text-green-600" size={24} />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-md p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Avg Rating</p>
              <p className="text-2xl font-bold text-gray-900">{metrics.avgRating.toFixed(1)}</p>
            </div>
            <div className="p-3 bg-yellow-50 rounded-lg">
              <Star className="text-yellow-600" size={24} />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-md p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Categories</p>
              <p className="text-2xl font-bold text-gray-900">{metrics.categories}</p>
            </div>
            <div className="p-3 bg-purple-50 rounded-lg">
              <TrendingUp className="text-purple-600" size={24} />
            </div>
          </div>
        </div>
      </div>

      {/* Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <PieChartComponent 
          data={categoryChartData} 
          title="Items by Category"
          dataKey="value"
          height={300}
        />
        <BarChartComponent 
          data={priceChartData} 
          title="Price Distribution"
          dataKey="value"
          height={300}
        />
      </div>

      {/* Data Table */}
      <div>
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Competitor Data</h3>
        <DataTable data={competitorData} />
      </div>
    </div>
  );
};

export default CompetitorDetail;