import React, { useState } from 'react';
import { 
  FileText, 
  Calendar, 
  Database, 
  Trash2, 
  Download,
  RefreshCw,
  ArrowLeft,
  BarChart3,
  TrendingUp,
  Users,
  Tag
} from 'lucide-react';
import { DataSource, ScrapedData, CategorySnapshot, RankingComparison } from '../../types';
import { useData } from '../../contexts/DataContext';
import { formatDate } from '../../utils/dateUtils';

interface DataSourceDetailProps {
  source: DataSource;
  onBack: () => void;
  onDelete: (sourceId: string) => void;
}

const DataSourceDetail: React.FC<DataSourceDetailProps> = ({ source, onBack, onDelete }) => {
  const { getDataBySource, categorySnapshots, rankingComparisons } = useData();
  const [isDeleting, setIsDeleting] = useState(false);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);

  // Get data associated with this source
  const sourceData = getDataBySource(source.id);
  
  // Get related snapshots and comparisons
  const relatedSnapshots = categorySnapshots.filter(snapshot => 
    snapshot.filename === source.filename
  );
  const relatedComparisons = rankingComparisons.filter(comparison =>
    comparison.currentSnapshot.filename === source.filename ||
    comparison.previousSnapshot?.filename === source.filename
  );

  // Calculate statistics
  const stats = {
    totalItems: sourceData.length,
    categories: Array.from(new Set(sourceData.map(item => item.category))).length,
    authors: Array.from(new Set(sourceData.map(item => item.author).filter(Boolean))).length,
    avgPrice: sourceData.filter(item => item.price).reduce((sum, item) => sum + (item.price || 0), 0) / sourceData.filter(item => item.price).length || 0,
    avgRating: sourceData.filter(item => item.rating).reduce((sum, item) => sum + (item.rating || 0), 0) / sourceData.filter(item => item.rating).length || 0,
    newItems: sourceData.filter(item => item.isNew).length,
    rankingItems: sourceData.filter(item => item.position).length
  };

  const handleDelete = async () => {
    setIsDeleting(true);
    try {
      await onDelete(source.id);
      onBack();
    } catch (error) {
      console.error('Failed to delete source:', error);
    } finally {
      setIsDeleting(false);
      setShowDeleteConfirm(false);
    }
  };

  const formatFileSize = (rowCount: number) => {
    const estimatedSize = rowCount * 100; // Rough estimate
    if (estimatedSize < 1024) return `${estimatedSize} B`;
    if (estimatedSize < 1024 * 1024) return `${(estimatedSize / 1024).toFixed(1)} KB`;
    return `${(estimatedSize / (1024 * 1024)).toFixed(1)} MB`;
  };

  const getFileType = () => {
    if (relatedSnapshots.length > 0 && relatedComparisons.length > 0) {
      return { label: 'Ranking Analysis File', color: 'bg-green-100 text-green-800' };
    } else if (relatedSnapshots.length > 0) {
      return { label: 'Ranking Snapshot File', color: 'bg-yellow-100 text-yellow-800' };
    } else {
      return { label: 'Standard Data File', color: 'bg-blue-100 text-blue-800' };
    }
  };

  const fileType = getFileType();

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <button
            onClick={onBack}
            className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg"
          >
            <ArrowLeft size={20} />
          </button>
          <div>
            <h2 className="text-xl font-semibold text-gray-900">File Details</h2>
            <p className="text-sm text-gray-600">Manage and analyze your uploaded CSV file</p>
          </div>
        </div>
        <button
          onClick={() => setShowDeleteConfirm(true)}
          className="flex items-center space-x-2 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700"
        >
          <Trash2 size={16} />
          <span>Delete File</span>
        </button>
      </div>

      {/* File Information */}
      <div className="bg-white rounded-lg shadow-md p-6">
        <div className="flex items-start space-x-4">
          <div className="p-3 bg-blue-100 rounded-lg">
            <FileText className="text-blue-600" size={24} />
          </div>
          <div className="flex-1">
            <h3 className="text-lg font-semibold text-gray-900">{source.filename}</h3>
            <p className="text-gray-600 mb-2">{source.name}</p>
            <div className="flex flex-wrap gap-2">
              <span className={`inline-flex items-center px-2 py-0.5 rounded text-xs font-medium ${fileType.color}`}>
                {fileType.label}
              </span>
              {source.category && (
                <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-gray-100 text-gray-800">
                  {source.category}
                </span>
              )}
            </div>
          </div>
        </div>

        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mt-6">
          <div className="text-center">
            <div className="text-lg font-semibold text-gray-900">{source.rowCount.toLocaleString()}</div>
            <div className="text-sm text-gray-600">Total Rows</div>
          </div>
          <div className="text-center">
            <div className="text-lg font-semibold text-gray-900">{formatFileSize(source.rowCount)}</div>
            <div className="text-sm text-gray-600">Est. Size</div>
          </div>
          <div className="text-center">
            <div className="text-lg font-semibold text-gray-900">
              {new Date(source.uploadDate).toLocaleDateString()}
            </div>
            <div className="text-sm text-gray-600">Upload Date</div>
          </div>
          <div className="text-center">
            <div className="text-lg font-semibold text-gray-900">{stats.totalItems}</div>
            <div className="text-sm text-gray-600">Valid Items</div>
          </div>
        </div>
      </div>

      {/* Data Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-white rounded-lg shadow-md p-6">
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-blue-100 rounded-lg">
              <Database className="text-blue-600" size={20} />
            </div>
            <div>
              <div className="text-lg font-semibold text-gray-900">{stats.categories}</div>
              <div className="text-sm text-gray-600">Categories</div>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-md p-6">
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-green-100 rounded-lg">
              <Users className="text-green-600" size={20} />
            </div>
            <div>
              <div className="text-lg font-semibold text-gray-900">{stats.authors}</div>
              <div className="text-sm text-gray-600">Authors</div>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-md p-6">
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-yellow-100 rounded-lg">
              <Tag className="text-yellow-600" size={20} />
            </div>
            <div>
              <div className="text-lg font-semibold text-gray-900">{stats.newItems}</div>
              <div className="text-sm text-gray-600">New Items</div>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-md p-6">
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-purple-100 rounded-lg">
              <TrendingUp className="text-purple-600" size={20} />
            </div>
            <div>
              <div className="text-lg font-semibold text-gray-900">{stats.rankingItems}</div>
              <div className="text-sm text-gray-600">Ranking Items</div>
            </div>
          </div>
        </div>
      </div>

      {/* Ranking Analysis Information */}
      {(relatedSnapshots.length > 0 || relatedComparisons.length > 0) && (
        <div className="bg-white rounded-lg shadow-md p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Ranking Analysis</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h4 className="font-medium text-gray-900 mb-2">Snapshots ({relatedSnapshots.length})</h4>
              {relatedSnapshots.length > 0 ? (
                <div className="space-y-2">
                  {relatedSnapshots.map(snapshot => (
                    <div key={snapshot.id} className="flex justify-between items-center p-2 bg-gray-50 rounded">
                      <span className="text-sm text-gray-900">{snapshot.category}</span>
                      <span className="text-xs text-gray-600">{snapshot.itemCount} items</span>
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-sm text-gray-600">No ranking snapshots found</p>
              )}
            </div>
            <div>
              <h4 className="font-medium text-gray-900 mb-2">Comparisons ({relatedComparisons.length})</h4>
              {relatedComparisons.length > 0 ? (
                <div className="space-y-2">
                  {relatedComparisons.map(comparison => (
                    <div key={comparison.id} className="flex justify-between items-center p-2 bg-gray-50 rounded">
                      <span className="text-sm text-gray-900">{comparison.category}</span>
                      <span className="text-xs text-gray-600">{comparison.changes.length} changes</span>
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-sm text-gray-600">No ranking comparisons found</p>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Price and Rating Statistics */}
      {(stats.avgPrice > 0 || stats.avgRating > 0) && (
        <div className="bg-white rounded-lg shadow-md p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Content Statistics</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {stats.avgPrice > 0 && (
              <div>
                <h4 className="font-medium text-gray-900 mb-2">Average Price</h4>
                <div className="text-2xl font-bold text-green-600">${stats.avgPrice.toFixed(2)}</div>
              </div>
            )}
            {stats.avgRating > 0 && (
              <div>
                <h4 className="font-medium text-gray-900 mb-2">Average Rating</h4>
                <div className="text-2xl font-bold text-yellow-600">{stats.avgRating.toFixed(1)} ⭐</div>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Delete Confirmation Modal */}
      {showDeleteConfirm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Confirm Deletion</h3>
            <p className="text-gray-600 mb-6">
              Are you sure you want to delete "{source.filename}" and all its associated data? 
              This will remove:
            </p>
            <ul className="list-disc list-inside text-sm text-gray-600 mb-6 space-y-1">
              <li>{stats.totalItems} data items</li>
              <li>{relatedSnapshots.length} ranking snapshots</li>
              <li>{relatedComparisons.length} ranking comparisons</li>
            </ul>
            <p className="text-red-600 text-sm font-medium mb-6">This action cannot be undone.</p>
            <div className="flex space-x-3">
              <button
                onClick={() => setShowDeleteConfirm(false)}
                className="flex-1 px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
              >
                Cancel
              </button>
              <button
                onClick={handleDelete}
                disabled={isDeleting}
                className="flex-1 px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 disabled:opacity-50"
              >
                {isDeleting ? 'Deleting...' : 'Delete'}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default DataSourceDetail;
