import React, { useState, useRef } from 'react';
import { Upload, FileText, AlertCircle, CheckCircle, X, Download, RefreshCw } from 'lucide-react';
import { dataManager } from '../../utils/dataManager';
import { CSVImportResult, DataSource, DataUpdateOptions } from '../../types';

interface DataImportProps {
  onImportComplete?: (result: CSVImportResult) => void;
  onClose?: () => void;
}

const DataImport: React.FC<DataImportProps> = ({ onImportComplete, onClose }) => {
  const [dragActive, setDragActive] = useState(false);
  const [importing, setImporting] = useState(false);
  const [importResult, setImportResult] = useState<CSVImportResult | null>(null);
  const [importOptions, setImportOptions] = useState<DataUpdateOptions>({
    mergeStrategy: 'append',
    category: 'Graphic Templates',
    source: 'Envato'
  });
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleDrag = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === 'dragenter' || e.type === 'dragover') {
      setDragActive(true);
    } else if (e.type === 'dragleave') {
      setDragActive(false);
    }
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);

    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      handleFile(e.dataTransfer.files[0]);
    }
  };

  const handleFileInput = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      handleFile(e.target.files[0]);
    }
  };

  const handleFile = async (file: File) => {
    if (!file.name.toLowerCase().endsWith('.csv')) {
      alert('Please select a CSV file');
      return;
    }

    setImporting(true);
    setImportResult(null);

    try {
      const content = await file.text();
      
      const dataSource: DataSource = {
        id: `source_${Date.now()}`,
        name: file.name.replace('.csv', ''),
        filename: file.name,
        uploadDate: new Date().toISOString(),
        rowCount: content.split('\n').length - 1,
        category: importOptions.category,
        competitor: importOptions.competitor
      };

      const result = dataManager.importData(content, dataSource, importOptions);
      setImportResult(result);
      
      if (onImportComplete) {
        onImportComplete(result);
      }
    } catch (error) {
      console.error('Import error:', error);
      setImportResult({
        success: false,
        data: [],
        errors: [error instanceof Error ? error.message : 'Unknown error occurred'],
        summary: { totalRows: 0, validRows: 0, invalidRows: 0 }
      });
    } finally {
      setImporting(false);
    }
  };

  const downloadSampleCSV = () => {
    const sampleData = [
      'title,description,url,category,price,rating,source,timestamp,tags',
      '"Premium Logo Template","High-quality logo design template","https://example.com/logo1","Graphic Templates",29.99,4.8,"Envato","2024-01-15T10:30:00Z","logo,template,premium"',
      '"Business Card Design","Professional business card template","https://example.com/card1","Graphic Templates",15.99,4.5,"Envato","2024-01-15T11:00:00Z","business,card,template"'
    ].join('\n');

    const blob = new Blob([sampleData], { type: 'text/csv' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'sample_data_format.csv';
    a.click();
    URL.revokeObjectURL(url);
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
        <div className="flex items-center justify-between p-6 border-b">
          <h2 className="text-xl font-semibold text-gray-900">Import CSV Data</h2>
          {onClose && (
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600"
            >
              <X size={24} />
            </button>
          )}
        </div>

        <div className="p-6 space-y-6">
          {/* Import Options */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium text-gray-900">Import Settings</h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Merge Strategy
                </label>
                <select
                  value={importOptions.mergeStrategy}
                  onChange={(e) => setImportOptions(prev => ({ 
                    ...prev, 
                    mergeStrategy: e.target.value as 'replace' | 'append' | 'update' 
                  }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="append">Append to existing data</option>
                  <option value="replace">Replace existing data</option>
                  <option value="update">Update existing items</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Default Category
                </label>
                <input
                  type="text"
                  value={importOptions.category || ''}
                  onChange={(e) => setImportOptions(prev => ({ ...prev, category: e.target.value }))}
                  placeholder="e.g., Graphic Templates"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Default Source
                </label>
                <input
                  type="text"
                  value={importOptions.source || ''}
                  onChange={(e) => setImportOptions(prev => ({ ...prev, source: e.target.value }))}
                  placeholder="e.g., Envato"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Competitor (Optional)
                </label>
                <input
                  type="text"
                  value={importOptions.competitor || ''}
                  onChange={(e) => setImportOptions(prev => ({ ...prev, competitor: e.target.value }))}
                  placeholder="e.g., Creative Market"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
            </div>
          </div>

          {/* File Upload Area */}
          <div
            className={`border-2 border-dashed rounded-lg p-8 text-center transition-colors ${
              dragActive 
                ? 'border-blue-500 bg-blue-50' 
                : 'border-gray-300 hover:border-gray-400'
            }`}
            onDragEnter={handleDrag}
            onDragLeave={handleDrag}
            onDragOver={handleDrag}
            onDrop={handleDrop}
          >
            <input
              ref={fileInputRef}
              type="file"
              accept=".csv"
              onChange={handleFileInput}
              className="hidden"
            />
            
            {importing ? (
              <div className="flex flex-col items-center space-y-4">
                <RefreshCw className="animate-spin text-blue-500" size={48} />
                <p className="text-lg font-medium text-gray-900">Importing data...</p>
              </div>
            ) : (
              <div className="space-y-4">
                <Upload className="mx-auto text-gray-400" size={48} />
                <div>
                  <p className="text-lg font-medium text-gray-900">
                    Drop your CSV file here, or{' '}
                    <button
                      onClick={() => fileInputRef.current?.click()}
                      className="text-blue-600 hover:text-blue-700 underline"
                    >
                      browse
                    </button>
                  </p>
                  <p className="text-sm text-gray-500 mt-2">
                    Supports CSV files with headers. Maximum file size: 10MB
                  </p>
                </div>
              </div>
            )}
          </div>

          {/* Sample CSV Download */}
          <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
            <div className="flex items-center space-x-3">
              <FileText className="text-gray-500" size={20} />
              <div>
                <p className="text-sm font-medium text-gray-900">Need help with CSV format?</p>
                <p className="text-xs text-gray-500">Download a sample CSV file to see the expected format</p>
              </div>
            </div>
            <button
              onClick={downloadSampleCSV}
              className="flex items-center space-x-2 px-3 py-2 text-sm bg-white border border-gray-300 rounded-md hover:bg-gray-50"
            >
              <Download size={16} />
              <span>Sample CSV</span>
            </button>
          </div>

          {/* Import Results */}
          {importResult && (
            <div className="space-y-4">
              <div className={`p-4 rounded-lg ${
                importResult.success ? 'bg-green-50 border border-green-200' : 'bg-red-50 border border-red-200'
              }`}>
                <div className="flex items-center space-x-2">
                  {importResult.success ? (
                    <CheckCircle className="text-green-600" size={20} />
                  ) : (
                    <AlertCircle className="text-red-600" size={20} />
                  )}
                  <h4 className={`font-medium ${
                    importResult.success ? 'text-green-900' : 'text-red-900'
                  }`}>
                    {importResult.success ? 'Import Successful!' : 'Import Failed'}
                  </h4>
                </div>
                
                <div className="mt-3 grid grid-cols-3 gap-4 text-sm">
                  <div>
                    <span className="font-medium">Total Rows:</span>
                    <span className="ml-2">{importResult.summary.totalRows}</span>
                  </div>
                  <div>
                    <span className="font-medium text-green-600">Valid:</span>
                    <span className="ml-2">{importResult.summary.validRows}</span>
                  </div>
                  <div>
                    <span className="font-medium text-red-600">Invalid:</span>
                    <span className="ml-2">{importResult.summary.invalidRows}</span>
                  </div>
                </div>
              </div>

              {importResult.errors.length > 0 && (
                <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                  <h5 className="font-medium text-yellow-900 mb-2">Import Warnings:</h5>
                  <ul className="text-sm text-yellow-800 space-y-1 max-h-32 overflow-y-auto">
                    {importResult.errors.slice(0, 10).map((error, index) => (
                      <li key={index}>• {error}</li>
                    ))}
                    {importResult.errors.length > 10 && (
                      <li>• ... and {importResult.errors.length - 10} more</li>
                    )}
                  </ul>
                </div>
              )}
            </div>
          )}
        </div>

        <div className="flex justify-end space-x-3 p-6 border-t bg-gray-50">
          {onClose && (
            <button
              onClick={onClose}
              className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
            >
              Close
            </button>
          )}
          {importResult?.success && (
            <button
              onClick={() => window.location.reload()}
              className="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700"
            >
              Refresh Dashboard
            </button>
          )}
        </div>
      </div>
    </div>
  );
};

export default DataImport;
