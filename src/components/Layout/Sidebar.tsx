import React from 'react';
import { 
  BarChart3, 
  Database, 
  Settings, 
  TrendingUp, 
  Filter,
  Download,
  Tag,
  Users,
  ChevronDown,
  ChevronRight
} from 'lucide-react';

interface SidebarProps {
  activeTab: string;
  onTabChange: (tab: string) => void;
}

const Sidebar: React.FC<SidebarProps> = ({ activeTab, onTabChange }) => {
  const [expandedMenus, setExpandedMenus] = React.useState<string[]>(['categories', 'competitors']);

  const toggleMenu = (menuId: string) => {
    setExpandedMenus(prev => 
      prev.includes(menuId) 
        ? prev.filter(id => id !== menuId)
        : [...prev, menuId]
    );
  };

  const mainMenuItems = [
    { id: 'overview', label: 'Overview', icon: BarChart3 },
    { id: 'data', label: 'All Data', icon: Database },
    { id: 'analytics', label: 'Analytics', icon: TrendingUp },
    { id: 'filters', label: 'Filters', icon: Filter },
    { id: 'exports', label: 'Exports', icon: Download }
  ];

  const categorySubItems = [
    { id: 'categories-overview', label: 'Categories Overview' },
    { id: 'categories-electronics', label: 'Electronics' },
    { id: 'categories-sports', label: 'Sports & Outdoors' },
    { id: 'categories-food', label: 'Food & Beverage' },
    { id: 'categories-fashion', label: 'Fashion' }
  ];

  const competitorSubItems = [
    { id: 'competitors-overview', label: 'Competitors Overview' },
    { id: 'competitors-techcorp', label: 'TechCorp' },
    { id: 'competitors-sportspro', label: 'SportsPro' },
    { id: 'competitors-gametech', label: 'GameTech' },
    { id: 'competitors-fitnesstech', label: 'FitnessTech' }
  ];

  return (
    <div className="w-64 bg-white shadow-lg h-full flex flex-col">
      <div className="p-6 border-b border-gray-200">
        <h1 className="text-xl font-bold text-gray-800">Data Dashboard</h1>
        <p className="text-sm text-gray-600 mt-1">Scraping Analytics</p>
      </div>
      
      <nav className="flex-1 p-4 space-y-1 overflow-y-auto">
        {/* Main Menu Items */}
        {mainMenuItems.map((item) => {
          const Icon = item.icon;
          return (
            <button
              key={item.id}
              onClick={() => onTabChange(item.id)}
              className={`w-full flex items-center space-x-3 px-4 py-3 rounded-lg text-left transition-colors ${
                activeTab === item.id
                  ? 'bg-blue-50 text-blue-700 border-r-2 border-blue-700'
                  : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
              }`}
            >
              <Icon size={20} />
              <span className="font-medium">{item.label}</span>
            </button>
          );
        })}

        {/* Categories Menu */}
        <div className="mt-4">
          <button
            onClick={() => toggleMenu('categories')}
            className="w-full flex items-center justify-between px-4 py-3 rounded-lg text-left transition-colors text-gray-600 hover:bg-gray-50 hover:text-gray-900"
          >
            <div className="flex items-center space-x-3">
              <Tag size={20} />
              <span className="font-medium">Categories</span>
            </div>
            {expandedMenus.includes('categories') ? 
              <ChevronDown size={16} /> : 
              <ChevronRight size={16} />
            }
          </button>
          
          {expandedMenus.includes('categories') && (
            <div className="ml-4 mt-1 space-y-1">
              {categorySubItems.map((item) => (
                <button
                  key={item.id}
                  onClick={() => onTabChange(item.id)}
                  className={`w-full flex items-center px-4 py-2 rounded-lg text-left text-sm transition-colors ${
                    activeTab === item.id
                      ? 'bg-blue-50 text-blue-700 border-r-2 border-blue-700'
                      : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
                  }`}
                >
                  <span>{item.label}</span>
                </button>
              ))}
            </div>
          )}
        </div>

        {/* Competitors Menu */}
        <div>
          <button
            onClick={() => toggleMenu('competitors')}
            className="w-full flex items-center justify-between px-4 py-3 rounded-lg text-left transition-colors text-gray-600 hover:bg-gray-50 hover:text-gray-900"
          >
            <div className="flex items-center space-x-3">
              <Users size={20} />
              <span className="font-medium">Competitors</span>
            </div>
            {expandedMenus.includes('competitors') ? 
              <ChevronDown size={16} /> : 
              <ChevronRight size={16} />
            }
          </button>
          
          {expandedMenus.includes('competitors') && (
            <div className="ml-4 mt-1 space-y-1">
              {competitorSubItems.map((item) => (
                <button
                  key={item.id}
                  onClick={() => onTabChange(item.id)}
                  className={`w-full flex items-center px-4 py-2 rounded-lg text-left text-sm transition-colors ${
                    activeTab === item.id
                      ? 'bg-blue-50 text-blue-700 border-r-2 border-blue-700'
                      : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
                  }`}
                >
                  <span>{item.label}</span>
                </button>
              ))}
            </div>
          )}
        </div>

        {/* Settings */}
        <div className="mt-4 pt-4 border-t border-gray-200">
          <button
            onClick={() => onTabChange('settings')}
            className={`w-full flex items-center space-x-3 px-4 py-3 rounded-lg text-left transition-colors ${
              activeTab === 'settings'
                ? 'bg-blue-50 text-blue-700 border-r-2 border-blue-700'
                : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
            }`}
          >
            <Settings size={20} />
            <span className="font-medium">Settings</span>
          </button>
        </div>
      </nav>
      
      <div className="p-4 border-t border-gray-200">
        <div className="flex items-center space-x-2 text-sm text-gray-600">
          <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
          <span>Live Data Sync</span>
        </div>
        <p className="text-xs text-gray-500 mt-1">Last updated: 2 min ago</p>
      </div>
    </div>
  );
};

export default Sidebar;