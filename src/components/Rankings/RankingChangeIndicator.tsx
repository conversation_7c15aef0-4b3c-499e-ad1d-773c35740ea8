import React from 'react';
import { TrendingUp, TrendingDown, Minus, Plus, X } from 'lucide-react';
import { RankingChange } from '../../types';

interface RankingChangeIndicatorProps {
  change: RankingChange;
  size?: 'sm' | 'md' | 'lg';
  showText?: boolean;
}

const RankingChangeIndicator: React.FC<RankingChangeIndicatorProps> = ({ 
  change, 
  size = 'md', 
  showText = true 
}) => {
  const getIcon = () => {
    const iconSize = size === 'sm' ? 14 : size === 'md' ? 16 : 20;
    
    switch (change.changeType) {
      case 'up':
        return <TrendingUp size={iconSize} />;
      case 'down':
        return <TrendingDown size={iconSize} />;
      case 'same':
        return <Minus size={iconSize} />;
      case 'new':
        return <Plus size={iconSize} />;
      case 'removed':
        return <X size={iconSize} />;
      default:
        return <Minus size={iconSize} />;
    }
  };

  const getColor = () => {
    switch (change.changeType) {
      case 'up':
        return 'text-green-600 bg-green-50';
      case 'down':
        return 'text-red-600 bg-red-50';
      case 'same':
        return 'text-gray-600 bg-gray-50';
      case 'new':
        return 'text-blue-600 bg-blue-50';
      case 'removed':
        return 'text-orange-600 bg-orange-50';
      default:
        return 'text-gray-600 bg-gray-50';
    }
  };

  const getText = () => {
    if (!showText) return null;
    
    switch (change.changeType) {
      case 'up':
        return `+${change.change}`;
      case 'down':
        return `${change.change}`;
      case 'same':
        return 'Same';
      case 'new':
        return 'New';
      case 'removed':
        return 'Removed';
      default:
        return '';
    }
  };

  const getTooltip = () => {
    switch (change.changeType) {
      case 'up':
        return `Moved up ${change.change} positions (${change.previousPosition} → ${change.currentPosition})`;
      case 'down':
        return `Moved down ${Math.abs(change.change)} positions (${change.previousPosition} → ${change.currentPosition})`;
      case 'same':
        return `Position unchanged (${change.currentPosition})`;
      case 'new':
        return `New item at position ${change.currentPosition}`;
      case 'removed':
        return `Removed from position ${change.previousPosition}`;
      default:
        return '';
    }
  };

  const sizeClasses = {
    sm: 'px-1.5 py-0.5 text-xs',
    md: 'px-2 py-1 text-sm',
    lg: 'px-3 py-1.5 text-base'
  };

  return (
    <div 
      className={`inline-flex items-center space-x-1 rounded-full font-medium ${getColor()} ${sizeClasses[size]}`}
      title={getTooltip()}
    >
      {getIcon()}
      {showText && <span>{getText()}</span>}
    </div>
  );
};

export default RankingChangeIndicator;
