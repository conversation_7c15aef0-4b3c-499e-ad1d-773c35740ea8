import React, { useState, useMemo } from 'react';
import { ChevronUp, ChevronDown, Filter, Download, Info } from 'lucide-react';
import { RankingComparison, RankingChangeItem } from '../../types';
import RankingChangeIndicator from './RankingChangeIndicator';

interface AllDataRankingTableProps {
  rankingComparisons: RankingComparison[];
  onExport?: () => void;
}

type SortField = 'title' | 'author' | 'pageOld' | 'pageNew' | 'orderOld' | 'orderNew' | 'change' | 'category' | 'link' | 'description' | 'competitor';
type SortDirection = 'asc' | 'desc';
type FilterType = 'all' | 'up' | 'down' | 'same' | 'new' | 'removed';

interface EnhancedRankingItem extends RankingChangeItem {
  category: string;
  pageOld?: number;
  pageNew?: number;
  description?: string;
  competitor?: string;
}

const AllDataRankingTable: React.FC<AllDataRankingTableProps> = ({ 
  rankingComparisons, 
  onExport 
}) => {
  const [sortField, setSortField] = useState<SortField>('change');
  const [sortDirection, setSortDirection] = useState<SortDirection>('desc');
  const [filter, setFilter] = useState<FilterType>('all');
  const [categoryFilter, setCategoryFilter] = useState<string>('all');
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 25;

  // Combine all ranking changes from all comparisons
  const allRankingData = useMemo(() => {
    const combinedData: EnhancedRankingItem[] = [];

    rankingComparisons.forEach(comparison => {
      comparison.changes.forEach(change => {
        // Find the item in current and previous snapshots to get page numbers and additional data
        const currentItem = comparison.currentSnapshot.items.find(item => item.link === change.link);
        const previousItem = comparison.previousSnapshot?.items.find(item => item.link === change.link);

        combinedData.push({
          ...change,
          category: comparison.category,
          pageOld: previousItem?.pageNumber,
          pageNew: currentItem?.pageNumber,
          // Add additional fields from the current item
          description: currentItem?.description || change.description,
          competitor: currentItem?.competitor || change.competitor
        });
      });
    });

    return combinedData;
  }, [rankingComparisons]);

  // Get unique categories for filter
  const categories = useMemo(() => {
    const uniqueCategories = Array.from(new Set(allRankingData.map(item => item.category)));
    return uniqueCategories.sort();
  }, [allRankingData]);

  const handleSort = (field: SortField) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('desc');
    }
  };

  const filteredData = useMemo(() => {
    return allRankingData.filter(item => {
      // Filter by change type
      if (filter !== 'all' && item.changeType !== filter) {
        return false;
      }
      
      // Filter by category
      if (categoryFilter !== 'all' && item.category !== categoryFilter) {
        return false;
      }
      
      return true;
    });
  }, [allRankingData, filter, categoryFilter]);

  const sortedData = useMemo(() => {
    return [...filteredData].sort((a, b) => {
      let aValue: any;
      let bValue: any;

      // Map sort fields to actual data properties
      switch (sortField) {
        case 'orderOld':
          aValue = a.previousPosition;
          bValue = b.previousPosition;
          break;
        case 'orderNew':
          aValue = a.currentPosition;
          bValue = b.currentPosition;
          break;
        default:
          aValue = a[sortField];
          bValue = b[sortField];
      }

      // Handle undefined values
      if (aValue === undefined) aValue = sortField.includes('Old') ? 999 : 0;
      if (bValue === undefined) bValue = sortField.includes('Old') ? 999 : 0;

      if (typeof aValue === 'string') {
        aValue = aValue.toLowerCase();
        bValue = bValue.toLowerCase();
      }

      if (aValue < bValue) return sortDirection === 'asc' ? -1 : 1;
      if (aValue > bValue) return sortDirection === 'asc' ? 1 : -1;
      return 0;
    });
  }, [filteredData, sortField, sortDirection]);

  const paginatedData = useMemo(() => {
    return sortedData.slice(
      (currentPage - 1) * itemsPerPage,
      currentPage * itemsPerPage
    );
  }, [sortedData, currentPage, itemsPerPage]);

  const totalPages = Math.ceil(sortedData.length / itemsPerPage);

  // Calculate summary statistics
  const summary = useMemo(() => {
    return {
      total: filteredData.length,
      movedUp: filteredData.filter(item => item.changeType === 'up').length,
      movedDown: filteredData.filter(item => item.changeType === 'down').length,
      stayedSame: filteredData.filter(item => item.changeType === 'same').length,
      newItems: filteredData.filter(item => item.changeType === 'new').length,
      removedItems: filteredData.filter(item => item.changeType === 'removed').length
    };
  }, [filteredData]);

  const SortIcon = ({ field }: { field: SortField }) => {
    if (sortField !== field) return null;
    return sortDirection === 'asc' ? <ChevronUp size={16} /> : <ChevronDown size={16} />;
  };

  const filterOptions = [
    { value: 'all', label: 'All Changes', count: summary.total },
    { value: 'up', label: 'Moved Up', count: summary.movedUp },
    { value: 'down', label: 'Moved Down', count: summary.movedDown },
    { value: 'same', label: 'Same Position', count: summary.stayedSame },
    { value: 'new', label: 'New Items', count: summary.newItems },
    { value: 'removed', label: 'Removed', count: summary.removedItems }
  ];

  if (allRankingData.length === 0) {
    return (
      <div className="bg-white rounded-lg shadow-md p-8 text-center">
        <Info className="mx-auto text-gray-400 mb-4" size={48} />
        <h3 className="text-lg font-semibold text-gray-900 mb-2">No Ranking Data Available</h3>
        <p className="text-gray-600 mb-4">
          To see ranking comparisons, import CSV files with the chronological naming pattern:
        </p>
        <code className="bg-gray-100 px-3 py-1 rounded text-sm">
          csvtitle_category_day_month_year.csv
        </code>
        <p className="text-sm text-gray-500 mt-4">
          Import at least two chronological files for the same category to see ranking changes.
        </p>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow-md overflow-hidden">
      {/* Header */}
      <div className="p-4 sm:p-6 border-b border-gray-200">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0">
          <div>
            <h3 className="text-lg font-semibold text-gray-900">All Data Ranking Changes</h3>
            <p className="text-sm text-gray-600 mt-1">
              Comprehensive view of ranking movements across all categories
            </p>
          </div>
          {onExport && (
            <button
              onClick={onExport}
              className="flex items-center space-x-2 px-3 py-2 text-sm bg-blue-600 text-white rounded-lg hover:bg-blue-700 self-start sm:self-auto"
            >
              <Download size={16} />
              <span>Export</span>
            </button>
          )}
        </div>

        {/* Summary Stats */}
        <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-6 gap-4 mt-4">
          {filterOptions.map(option => (
            <div key={option.value} className="text-center">
              <div className="text-lg font-semibold text-gray-900">{option.count}</div>
              <div className="text-xs text-gray-600">{option.label}</div>
            </div>
          ))}
        </div>
      </div>

      {/* Filters */}
      <div className="p-4 bg-gray-50 border-b border-gray-200">
        <div className="flex flex-col space-y-4">
          {/* Change Type Filter */}
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2">
              <Filter size={16} className="text-gray-500" />
              <span className="text-sm font-medium text-gray-700">Status:</span>
            </div>
            <div className="flex flex-wrap gap-2">
              {filterOptions.map(option => (
                <button
                  key={option.value}
                  onClick={() => {
                    setFilter(option.value as FilterType);
                    setCurrentPage(1);
                  }}
                  className={`px-3 py-1 text-xs rounded-full transition-colors ${
                    filter === option.value
                      ? 'bg-blue-600 text-white'
                      : 'bg-white text-gray-600 hover:bg-gray-100'
                  }`}
                >
                  {option.label} ({option.count})
                </button>
              ))}
            </div>
          </div>

          {/* Category Filter */}
          <div className="flex items-center space-x-4">
            <span className="text-sm font-medium text-gray-700">Category:</span>
            <select
              value={categoryFilter}
              onChange={(e) => {
                setCategoryFilter(e.target.value);
                setCurrentPage(1);
              }}
              className="px-3 py-1 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="all">All Categories</option>
              {categories.map(category => (
                <option key={category} value={category}>{category}</option>
              ))}
            </select>
          </div>
        </div>
      </div>

      {/* Table */}
      <div className="overflow-x-auto">
        <div className="min-w-full inline-block align-middle">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th
                  className="px-3 sm:px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                  onClick={() => handleSort('title')}
                >
                  <div className="flex items-center space-x-1">
                    <span>Title</span>
                    <SortIcon field="title" />
                  </div>
                </th>
              <th
                className="px-3 sm:px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                onClick={() => handleSort('author')}
              >
                <div className="flex items-center space-x-1">
                  <span>Author</span>
                  <SortIcon field="author" />
                </div>
              </th>
              <th
                className="px-3 sm:px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                onClick={() => handleSort('pageOld')}
              >
                <div className="flex items-center space-x-1">
                  <span className="hidden sm:inline">Page Old</span>
                  <span className="sm:hidden">P.Old</span>
                  <SortIcon field="pageOld" />
                </div>
              </th>
              <th
                className="px-3 sm:px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                onClick={() => handleSort('pageNew')}
              >
                <div className="flex items-center space-x-1">
                  <span className="hidden sm:inline">Page New</span>
                  <span className="sm:hidden">P.New</span>
                  <SortIcon field="pageNew" />
                </div>
              </th>
              <th
                className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                onClick={() => handleSort('orderOld')}
              >
                <div className="flex items-center space-x-1">
                  <span>Order Old</span>
                  <SortIcon field="orderOld" />
                </div>
              </th>
              <th
                className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                onClick={() => handleSort('orderNew')}
              >
                <div className="flex items-center space-x-1">
                  <span>Order New</span>
                  <SortIcon field="orderNew" />
                </div>
              </th>
              <th 
                className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                onClick={() => handleSort('change')}
              >
                <div className="flex items-center space-x-1">
                  <span>Change</span>
                  <SortIcon field="change" />
                </div>
              </th>
              <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Status
              </th>
              <th
                className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                onClick={() => handleSort('link')}
              >
                <div className="flex items-center space-x-1">
                  <span>Link</span>
                  <SortIcon field="link" />
                </div>
              </th>
              <th
                className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                onClick={() => handleSort('description')}
              >
                <div className="flex items-center space-x-1">
                  <span>Description</span>
                  <SortIcon field="description" />
                </div>
              </th>
              <th
                className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                onClick={() => handleSort('competitor')}
              >
                <div className="flex items-center space-x-1">
                  <span>Competitor</span>
                  <SortIcon field="competitor" />
                </div>
              </th>
              <th
                className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                onClick={() => handleSort('category')}
              >
                <div className="flex items-center space-x-1">
                  <span>Category</span>
                  <SortIcon field="category" />
                </div>
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {paginatedData.map((item, index) => (
              <tr key={`${item.link}-${index}`} className="hover:bg-gray-50">
                <td className="px-4 py-3">
                  <div className="text-sm font-medium text-gray-900 truncate max-w-xs">
                    {item.title}
                  </div>
                </td>
                <td className="px-4 py-3">
                  <div className="text-sm text-gray-600">
                    {item.author || '-'}
                  </div>
                </td>
                <td className="px-4 py-3">
                  <div className="text-sm text-gray-900">
                    {item.pageOld || '-'}
                  </div>
                </td>
                <td className="px-4 py-3">
                  <div className="text-sm text-gray-900">
                    {item.pageNew || '-'}
                  </div>
                </td>
                <td className="px-4 py-3">
                  <div className="text-sm text-gray-900">
                    {item.previousPosition || '-'}
                  </div>
                </td>
                <td className="px-4 py-3">
                  <div className="text-sm text-gray-900">
                    {item.currentPosition > 0 ? item.currentPosition : '-'}
                  </div>
                </td>
                <td className="px-4 py-3">
                  <div className="text-sm font-medium text-gray-900">
                    {item.changeType === 'new' || item.changeType === 'removed' ? '-' : 
                     item.change > 0 ? `+${item.change}` : item.change}
                  </div>
                </td>
                <td className="px-4 py-3">
                  <RankingChangeIndicator
                    change={{
                      previousPosition: item.previousPosition,
                      currentPosition: item.currentPosition,
                      change: item.change,
                      changeType: item.changeType
                    }}
                    size="sm"
                  />
                </td>
                <td className="px-4 py-3">
                  {item.link ? (
                    <a
                      href={item.link}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-blue-600 hover:text-blue-800 text-sm truncate max-w-xs block"
                    >
                      {item.link.length > 25 ? `${item.link.substring(0, 25)}...` : item.link}
                    </a>
                  ) : '-'}
                </td>
                <td className="px-4 py-3">
                  <div className="text-sm text-gray-900 truncate max-w-xs">
                    {item.description || '-'}
                  </div>
                </td>
                <td className="px-4 py-3">
                  <div className="text-sm text-gray-900">
                    {item.competitor || '-'}
                  </div>
                </td>
                <td className="px-4 py-3">
                  <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-gray-100 text-gray-800">
                    {item.category}
                  </span>
                </td>
              </tr>
            ))}
          </tbody>
          </table>
        </div>
      </div>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="px-6 py-3 bg-gray-50 border-t border-gray-200">
          <div className="flex items-center justify-between">
            <div className="text-sm text-gray-700">
              Showing {(currentPage - 1) * itemsPerPage + 1} to {Math.min(currentPage * itemsPerPage, sortedData.length)} of {sortedData.length} results
            </div>
            <div className="flex space-x-2">
              <button
                onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                disabled={currentPage === 1}
                className="px-3 py-1 text-sm bg-white border border-gray-300 rounded hover:bg-gray-50 disabled:opacity-50"
              >
                Previous
              </button>
              <span className="px-3 py-1 text-sm">
                Page {currentPage} of {totalPages}
              </span>
              <button
                onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                disabled={currentPage === totalPages}
                className="px-3 py-1 text-sm bg-white border border-gray-300 rounded hover:bg-gray-50 disabled:opacity-50"
              >
                Next
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default AllDataRankingTable;
