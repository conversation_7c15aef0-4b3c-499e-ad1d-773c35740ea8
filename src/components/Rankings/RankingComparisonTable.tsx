import React, { useState } from 'react';
import { ChevronUp, ChevronDown, Filter, Download } from 'lucide-react';
import { RankingComparison, RankingChangeItem } from '../../types';
import RankingChangeIndicator from './RankingChangeIndicator';
import Pagination from '../Common/Pagination';

interface RankingComparisonTableProps {
  comparison: RankingComparison;
  onExport?: () => void;
}

type SortField = 'title' | 'author' | 'currentPosition' | 'previousPosition' | 'change' | 'link' | 'description' | 'competitor';
type SortDirection = 'asc' | 'desc';
type FilterType = 'all' | 'up' | 'down' | 'same' | 'new' | 'removed';

const RankingComparisonTable: React.FC<RankingComparisonTableProps> = ({ 
  comparison, 
  onExport 
}) => {
  const [sortField, setSortField] = useState<SortField>('change');
  const [sortDirection, setSortDirection] = useState<SortDirection>('desc');
  const [filter, setFilter] = useState<FilterType>('all');
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 10;

  const handleSort = (field: SortField) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('desc');
    }
  };

  const filteredData = comparison.changes.filter(item => {
    if (filter === 'all') return true;
    return item.changeType === filter;
  });

  const sortedData = [...filteredData].sort((a, b) => {
    let aValue: any = a[sortField];
    let bValue: any = b[sortField];

    // Handle undefined values
    if (aValue === undefined) aValue = sortField === 'previousPosition' ? 999 : 0;
    if (bValue === undefined) bValue = sortField === 'previousPosition' ? 999 : 0;

    if (typeof aValue === 'string') {
      aValue = aValue.toLowerCase();
      bValue = bValue.toLowerCase();
    }

    if (aValue < bValue) return sortDirection === 'asc' ? -1 : 1;
    if (aValue > bValue) return sortDirection === 'asc' ? 1 : -1;
    return 0;
  });

  const paginatedData = sortedData.slice(
    (currentPage - 1) * itemsPerPage,
    currentPage * itemsPerPage
  );

  const totalPages = Math.ceil(sortedData.length / itemsPerPage);

  const SortIcon = ({ field }: { field: SortField }) => {
    if (sortField !== field) return null;
    return sortDirection === 'asc' ? <ChevronUp size={16} /> : <ChevronDown size={16} />;
  };

  const filterOptions = [
    { value: 'all', label: 'All Changes', count: comparison.changes.length },
    { value: 'up', label: 'Moved Up', count: comparison.summary.movedUp },
    { value: 'down', label: 'Moved Down', count: comparison.summary.movedDown },
    { value: 'same', label: 'Same Position', count: comparison.summary.stayedSame },
    { value: 'new', label: 'New Items', count: comparison.summary.newItems },
    { value: 'removed', label: 'Removed', count: comparison.summary.removedItems }
  ];

  return (
    <div className="bg-white rounded-lg shadow-md overflow-hidden">
      {/* Header */}
      <div className="p-6 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-lg font-semibold text-gray-900">Ranking Changes</h3>
            <p className="text-sm text-gray-600 mt-1">
              Comparison between {comparison.previousSnapshot ? 
                new Date(comparison.previousSnapshot.timestamp).toLocaleDateString() : 'N/A'
              } and {new Date(comparison.currentSnapshot.timestamp).toLocaleDateString()}
            </p>
          </div>
          {onExport && (
            <button
              onClick={onExport}
              className="flex items-center space-x-2 px-3 py-2 text-sm bg-blue-600 text-white rounded-lg hover:bg-blue-700"
            >
              <Download size={16} />
              <span>Export</span>
            </button>
          )}
        </div>

        {/* Summary Stats */}
        <div className="grid grid-cols-2 md:grid-cols-6 gap-4 mt-4">
          {filterOptions.map(option => (
            <div key={option.value} className="text-center">
              <div className="text-lg font-semibold text-gray-900">{option.count}</div>
              <div className="text-xs text-gray-600">{option.label}</div>
            </div>
          ))}
        </div>
      </div>

      {/* Filters */}
      <div className="p-4 bg-gray-50 border-b border-gray-200">
        <div className="flex items-center space-x-4">
          <div className="flex items-center space-x-2">
            <Filter size={16} className="text-gray-500" />
            <span className="text-sm font-medium text-gray-700">Filter:</span>
          </div>
          <div className="flex flex-wrap gap-2">
            {filterOptions.map(option => (
              <button
                key={option.value}
                onClick={() => {
                  setFilter(option.value as FilterType);
                  setCurrentPage(1);
                }}
                className={`px-3 py-1 text-xs rounded-full transition-colors ${
                  filter === option.value
                    ? 'bg-blue-600 text-white'
                    : 'bg-white text-gray-600 hover:bg-gray-100'
                }`}
              >
                {option.label} ({option.count})
              </button>
            ))}
          </div>
        </div>
      </div>

      {/* Table */}
      <div className="overflow-x-auto">
        <div className="min-w-full inline-block align-middle">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th
                  className="px-4 sm:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                  onClick={() => handleSort('title')}
                >
                  <div className="flex items-center space-x-1">
                    <span>Title</span>
                    <SortIcon field="title" />
                  </div>
                </th>
              <th 
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                onClick={() => handleSort('author')}
              >
                <div className="flex items-center space-x-1">
                  <span>Author</span>
                  <SortIcon field="author" />
                </div>
              </th>
              <th 
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                onClick={() => handleSort('previousPosition')}
              >
                <div className="flex items-center space-x-1">
                  <span>Previous</span>
                  <SortIcon field="previousPosition" />
                </div>
              </th>
              <th 
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                onClick={() => handleSort('currentPosition')}
              >
                <div className="flex items-center space-x-1">
                  <span>Current</span>
                  <SortIcon field="currentPosition" />
                </div>
              </th>
              <th
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                onClick={() => handleSort('change')}
              >
                <div className="flex items-center space-x-1">
                  <span>Change</span>
                  <SortIcon field="change" />
                </div>
              </th>
              <th
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                onClick={() => handleSort('link')}
              >
                <div className="flex items-center space-x-1">
                  <span>Link</span>
                  <SortIcon field="link" />
                </div>
              </th>
              <th
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                onClick={() => handleSort('description')}
              >
                <div className="flex items-center space-x-1">
                  <span>Description</span>
                  <SortIcon field="description" />
                </div>
              </th>
              <th
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                onClick={() => handleSort('competitor')}
              >
                <div className="flex items-center space-x-1">
                  <span>Competitor</span>
                  <SortIcon field="competitor" />
                </div>
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {paginatedData.map((item) => (
              <tr key={item.link} className="hover:bg-gray-50">
                <td className="px-6 py-4">
                  <div className="text-sm font-medium text-gray-900 truncate max-w-xs">
                    {item.title}
                  </div>
                </td>
                <td className="px-6 py-4">
                  <div className="text-sm text-gray-600">
                    {item.author || '-'}
                  </div>
                </td>
                <td className="px-6 py-4">
                  <div className="text-sm text-gray-900">
                    {item.previousPosition || '-'}
                  </div>
                </td>
                <td className="px-6 py-4">
                  <div className="text-sm text-gray-900">
                    {item.currentPosition > 0 ? item.currentPosition : '-'}
                  </div>
                </td>
                <td className="px-6 py-4">
                  <RankingChangeIndicator
                    change={{
                      previousPosition: item.previousPosition,
                      currentPosition: item.currentPosition,
                      change: item.change,
                      changeType: item.changeType
                    }}
                    size="sm"
                  />
                </td>
                <td className="px-6 py-4">
                  {item.link ? (
                    <a
                      href={item.link}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-blue-600 hover:text-blue-800 text-sm truncate max-w-xs block"
                    >
                      {item.link.length > 30 ? `${item.link.substring(0, 30)}...` : item.link}
                    </a>
                  ) : '-'}
                </td>
                <td className="px-6 py-4">
                  <div className="text-sm text-gray-900 truncate max-w-xs">
                    {item.description || '-'}
                  </div>
                </td>
                <td className="px-6 py-4">
                  <div className="text-sm text-gray-900">
                    {item.competitor || '-'}
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
          </table>
        </div>
      </div>

      {/* Pagination */}
      <Pagination
        currentPage={currentPage}
        totalPages={totalPages}
        totalItems={sortedData.length}
        itemsPerPage={itemsPerPage}
        onPageChange={setCurrentPage}
      />
    </div>
  );
};

export default RankingComparisonTable;
