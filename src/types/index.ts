export interface ScrapedData {
  id: string;
  title: string;
  description: string;
  url: string;
  category: string;
  price?: number;
  rating?: number;
  timestamp: string;
  source: string;
  tags: string[];
  metadata: Record<string, any>;
  competitor?: string;
}

export interface MetricData {
  label: string;
  value: number;
  change: number;
  changeType: 'increase' | 'decrease' | 'neutral';
  icon: string;
}

export interface ChartData {
  name: string;
  value: number;
  date?: string;
  category?: string;
  competitor?: string;
}

export interface FilterOptions {
  category: string;
  source: string;
  competitor: string;
  dateRange: {
    start: Date | null;
    end: Date | null;
  };
  priceRange: {
    min: number;
    max: number;
  };
  search: string;
}

export interface ExportOptions {
  format: 'csv' | 'excel' | 'pdf';
  includeFilters: boolean;
  dateRange?: {
    start: Date;
    end: Date;
  };
}

export interface CategoryData {
  id: string;
  name: string;
  description: string;
  itemCount: number;
  averagePrice: number;
  lastUpdated: string;
  color: string;
}

export interface CompetitorData {
  id: string;
  name: string;
  website: string;
  itemCount: number;
  averagePrice: number;
  averageRating: number;
  lastUpdated: string;
  color: string;
}

// Data import and management interfaces
export interface CSVImportResult {
  success: boolean;
  data: ScrapedData[];
  errors: string[];
  summary: {
    totalRows: number;
    validRows: number;
    invalidRows: number;
  };
}

export interface DataSource {
  id: string;
  name: string;
  filename: string;
  uploadDate: string;
  rowCount: number;
  category?: string;
  competitor?: string;
}

export interface DataUpdateOptions {
  mergeStrategy: 'replace' | 'append' | 'update';
  category?: string;
  competitor?: string;
  source?: string;
}