import React, { useState, useEffect, useMemo } from 'react';
import { Upload, Database } from 'lucide-react';
import Sidebar from './components/Layout/Sidebar';
import Header from './components/Layout/Header';
import MetricsCard from './components/Metrics/MetricsCard';
import LineChartComponent from './components/Charts/LineChart';
import BarChartComponent from './components/Charts/BarChart';
import PieChartComponent from './components/Charts/PieChart';
import DataTable from './components/Data/DataTable';
import FilterPanel from './components/Filters/FilterPanel';
import ExportPanel from './components/Export/ExportPanel';
import CategoryOverview from './components/Categories/CategoryOverview';
import CategoryDetail from './components/Categories/CategoryDetail';
import CompetitorOverview from './components/Competitors/CompetitorOverview';
import CompetitorDetail from './components/Competitors/CompetitorDetail';
import DataImport from './components/Data/DataImport';
import DataSourceManager from './components/Data/DataSourceManager';
import AllDataRankingTable from './components/Rankings/AllDataRankingTable';
import { useData } from './contexts/DataContext';
import { ScrapedData, FilterOptions, CSVImportResult } from './types';

function AppContent() {
  const {
    scrapedData,
    categories,
    competitors,
    metrics,
    categoryChartData,
    competitorChartData,
    chartData,
    rankingComparisons,
    isLoading: dataLoading,
    refreshData
  } = useData();

  const [activeTab, setActiveTab] = useState('overview');
  const [isLoading, setIsLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [showImport, setShowImport] = useState(false);
  const [showDataSourceManager, setShowDataSourceManager] = useState(false);
  const [filters, setFilters] = useState<FilterOptions>({
    category: '',
    source: '',
    competitor: '',
    dateRange: { start: null, end: null },
    priceRange: { min: 0, max: 0 },
    search: ''
  });

  // Sync search term with filters
  useEffect(() => {
    setFilters(prev => ({ ...prev, search: searchTerm }));
  }, [searchTerm]);

  // Get unique categories, sources, and competitors for filters
  const filterCategories = useMemo(() => 
    Array.from(new Set(scrapedData.map(item => item.category))).sort(),
    [scrapedData]
  );

  const sources = useMemo(() => 
    Array.from(new Set(scrapedData.map(item => item.source))).sort(),
    [scrapedData]
  );

  const filterCompetitors = useMemo(() => 
    Array.from(new Set(scrapedData.map(item => item.competitor).filter(Boolean))).sort(),
    [scrapedData]
  );

  // Filter data based on current filters
  const filteredData = useMemo(() => {
    return scrapedData.filter(item => {
      // Search filter
      if (filters.search && !item.title.toLowerCase().includes(filters.search.toLowerCase()) &&
          !item.description.toLowerCase().includes(filters.search.toLowerCase())) {
        return false;
      }

      // Category filter
      if (filters.category && item.category !== filters.category) {
        return false;
      }

      // Competitor filter
      if (filters.competitor && item.competitor !== filters.competitor) {
        return false;
      }

      // Source filter
      if (filters.source && item.source !== filters.source) {
        return false;
      }

      // Date range filter
      if (filters.dateRange.start || filters.dateRange.end) {
        const itemDate = new Date(item.timestamp);
        if (filters.dateRange.start && itemDate < filters.dateRange.start) {
          return false;
        }
        if (filters.dateRange.end && itemDate > filters.dateRange.end) {
          return false;
        }
      }

      // Price range filter
      if (filters.priceRange.min > 0 || filters.priceRange.max > 0) {
        if (!item.price) return false;
        if (filters.priceRange.min > 0 && item.price < filters.priceRange.min) {
          return false;
        }
        if (filters.priceRange.max > 0 && item.price > filters.priceRange.max) {
          return false;
        }
      }

      return true;
    });
  }, [scrapedData, filters]);

  const handleRefresh = async () => {
    setIsLoading(true);
    refreshData();
    // Simulate additional loading time
    await new Promise(resolve => setTimeout(resolve, 500));
    setIsLoading(false);
  };

  const handleItemClick = (item: ScrapedData) => {
    console.log('Item clicked:', item);
    // You can implement a modal or detail view here
  };

  const handleImportComplete = (result: CSVImportResult) => {
    if (result.success) {
      refreshData();
      setShowImport(false);
    }
  };

  const renderContent = () => {
    switch (activeTab) {
      case 'overview':
        return (
          <div className="space-y-6">
            {/* Import Button */}
            <div className="flex justify-between items-center">
              <h1 className="text-2xl font-bold text-gray-900">Dashboard Overview</h1>
              <button
                onClick={() => setShowImport(true)}
                className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                <Upload size={20} />
                <span>Import CSV Data</span>
              </button>
            </div>

            {/* Metrics */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {metrics.map((metric, index) => (
                <MetricsCard key={index} metric={metric} />
              ))}
            </div>

            {/* Charts */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <LineChartComponent 
                data={chartData} 
                title="Scraping Activity Over Time"
                dataKey="value"
                height={300}
              />
              <PieChartComponent 
                data={categoryChartData} 
                title="Data by Category"
                dataKey="value"
                height={300}
              />
            </div>

            {/* Recent Data Table */}
            <div>
              <h2 className="text-xl font-semibold text-gray-900 mb-4">Recent Data</h2>
              <DataTable 
                data={filteredData.slice(0, 5)} 
                onItemClick={handleItemClick}
              />
            </div>
          </div>
        );

      case 'data':
        return (
          <div className="space-y-6">
            <div className="flex justify-between items-center">
              <h2 className="text-xl font-semibold text-gray-900">All Scraped Data</h2>
              <div className="flex items-center space-x-4">
                <p className="text-sm text-gray-600">
                  Showing {filteredData.length} of {scrapedData.length} items
                </p>
                <button
                  onClick={() => setShowDataSourceManager(true)}
                  className="flex items-center space-x-2 px-3 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors text-sm"
                >
                  <Database size={16} />
                  <span>Manage Files</span>
                </button>
                <button
                  onClick={() => setShowImport(true)}
                  className="flex items-center space-x-2 px-3 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm"
                >
                  <Upload size={16} />
                  <span>Import</span>
                </button>
              </div>
            </div>

            {/* Ranking Comparison Table */}
            <AllDataRankingTable
              rankingComparisons={rankingComparisons}
              onExport={() => {
                // TODO: Implement export functionality
                console.log('Export all ranking data:', rankingComparisons);
              }}
            />

            {/* All Data Table */}
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Complete Data Set</h3>
              <DataTable data={filteredData} onItemClick={handleItemClick} />
            </div>
          </div>
        );

      case 'analytics':
        return (
          <div className="space-y-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">Analytics Dashboard</h2>
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <BarChartComponent 
                data={categoryChartData} 
                title="Items by Category"
                dataKey="value"
                height={300}
              />
              <BarChartComponent 
                data={competitorChartData} 
                title="Items by Competitor"
                dataKey="value"
                height={300}
              />
            </div>
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <PieChartComponent 
                data={categoryChartData} 
                title="Category Distribution"
                dataKey="value"
                height={300}
              />
              <PieChartComponent 
                data={competitorChartData} 
                title="Competitor Distribution"
                dataKey="value"
                height={300}
              />
            </div>
          </div>
        );

      case 'filters':
        return (
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <div className="lg:col-span-1">
              <FilterPanel 
                filters={filters}
                onFiltersChange={setFilters}
                categories={filterCategories}
                sources={sources}
                competitors={filterCompetitors}
              />
            </div>
            <div className="lg:col-span-2">
              <div className="space-y-6">
                <div className="flex justify-between items-center">
                  <h2 className="text-xl font-semibold text-gray-900">Filtered Results</h2>
                  <p className="text-sm text-gray-600">
                    {filteredData.length} items match your filters
                  </p>
                </div>
                <DataTable data={filteredData} onItemClick={handleItemClick} />
              </div>
            </div>
          </div>
        );

      case 'exports':
        return (
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <div className="lg:col-span-1">
              <ExportPanel data={scrapedData} filteredData={filteredData} />
            </div>
            <div className="lg:col-span-2">
              <div className="space-y-6">
                <h2 className="text-xl font-semibold text-gray-900">Export Preview</h2>
                <DataTable data={filteredData.slice(0, 10)} onItemClick={handleItemClick} />
              </div>
            </div>
          </div>
        );

      // Categories
      case 'categories-overview':
        return <CategoryOverview />;

      // Competitors
      case 'competitors-overview':
        return <CompetitorOverview />;

      case 'settings':
        return (
          <div className="space-y-6">
            <h2 className="text-xl font-semibold text-gray-900">Settings</h2>
            <div className="bg-white rounded-lg shadow-md p-6">
              <p className="text-gray-600">Settings panel coming soon...</p>
            </div>
          </div>
        );

      default:
        // Handle dynamic category routes
        if (activeTab.startsWith('categories-') && activeTab !== 'categories-overview') {
          const categorySlug = activeTab.replace('categories-', '');
          const category = categories.find(cat =>
            cat.name.toLowerCase().replace(/\s+/g, '-').replace(/[^a-z0-9-]/g, '') === categorySlug
          );
          if (category) {
            return <CategoryDetail categoryName={category.name} onBack={() => setActiveTab('categories-overview')} />;
          }
        }

        // Handle dynamic competitor routes
        if (activeTab.startsWith('competitors-') && activeTab !== 'competitors-overview') {
          const competitorSlug = activeTab.replace('competitors-', '');
          const competitor = competitors.find(comp =>
            comp.name.toLowerCase().replace(/\s+/g, '-').replace(/[^a-z0-9-]/g, '') === competitorSlug
          );
          if (competitor) {
            return <CompetitorDetail competitorName={competitor.name} onBack={() => setActiveTab('competitors-overview')} />;
          }
        }

        return null;
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 flex">
      {/* Sidebar */}
      <Sidebar activeTab={activeTab} onTabChange={setActiveTab} />
      
      {/* Main Content */}
      <div className="flex-1 flex flex-col">
        {/* Header */}
        <Header 
          onRefresh={handleRefresh}
          isLoading={isLoading || dataLoading}
          searchTerm={searchTerm}
          onSearchChange={setSearchTerm}
        />
        
        {/* Content */}
        <main className="flex-1 p-6 overflow-auto">
          {renderContent()}
        </main>
      </div>

      {/* Data Import Modal */}
      {showImport && (
        <DataImport
          onImportComplete={handleImportComplete}
          onClose={() => setShowImport(false)}
        />
      )}

      {/* Data Source Manager Modal */}
      {showDataSourceManager && (
        <DataSourceManager
          onClose={() => setShowDataSourceManager(false)}
        />
      )}
    </div>
  );
}

export default AppContent;
