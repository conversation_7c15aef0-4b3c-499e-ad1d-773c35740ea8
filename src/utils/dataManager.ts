import { ScrapedData, CSVImportResult, DataSource, DataUpdateOptions, CategoryData, CompetitorData } from '../types';

// Local storage keys
const STORAGE_KEYS = {
  SCRAPED_DATA: 'dashboard_scraped_data',
  DATA_SOURCES: 'dashboard_data_sources',
  CATEGORIES: 'dashboard_categories',
  COMPETITORS: 'dashboard_competitors',
  LAST_UPDATE: 'dashboard_last_update'
};

// Data validation and transformation utilities
export class DataManager {
  private static instance: DataManager;
  private data: ScrapedData[] = [];
  private dataSources: DataSource[] = [];
  private categories: CategoryData[] = [];
  private competitors: CompetitorData[] = [];

  private constructor() {
    this.loadFromStorage();
  }

  static getInstance(): DataManager {
    if (!DataManager.instance) {
      DataManager.instance = new DataManager();
    }
    return DataManager.instance;
  }

  // CSV parsing and validation
  parseCSV(csvContent: string, options: DataUpdateOptions = { mergeStrategy: 'append' }): CSVImportResult {
    const lines = csvContent.trim().split('\n');
    const headers = lines[0].split(',').map(h => h.trim().replace(/"/g, ''));
    
    const result: CSVImportResult = {
      success: false,
      data: [],
      errors: [],
      summary: {
        totalRows: lines.length - 1,
        validRows: 0,
        invalidRows: 0
      }
    };

    // Map common CSV headers to our data structure
    const headerMap = this.createHeaderMap(headers);
    
    for (let i = 1; i < lines.length; i++) {
      try {
        const values = this.parseCSVLine(lines[i]);
        const item = this.transformCSVRowToScrapedData(values, headerMap, options);
        
        if (this.validateScrapedData(item)) {
          result.data.push(item);
          result.summary.validRows++;
        } else {
          result.errors.push(`Row ${i + 1}: Invalid data format`);
          result.summary.invalidRows++;
        }
      } catch (error) {
        result.errors.push(`Row ${i + 1}: ${error instanceof Error ? error.message : 'Parse error'}`);
        result.summary.invalidRows++;
      }
    }

    result.success = result.summary.validRows > 0;
    return result;
  }

  private createHeaderMap(headers: string[]): Record<string, number> {
    const map: Record<string, number> = {};
    
    headers.forEach((header, index) => {
      const normalizedHeader = header.toLowerCase().replace(/[^a-z0-9]/g, '');
      
      // Map common variations to standard fields
      if (normalizedHeader.includes('title') || normalizedHeader.includes('name')) {
        map.title = index;
      } else if (normalizedHeader.includes('description') || normalizedHeader.includes('desc')) {
        map.description = index;
      } else if (normalizedHeader.includes('url') || normalizedHeader.includes('link')) {
        map.url = index;
      } else if (normalizedHeader.includes('category') || normalizedHeader.includes('cat')) {
        map.category = index;
      } else if (normalizedHeader.includes('price') || normalizedHeader.includes('cost')) {
        map.price = index;
      } else if (normalizedHeader.includes('rating') || normalizedHeader.includes('score')) {
        map.rating = index;
      } else if (normalizedHeader.includes('source') || normalizedHeader.includes('site')) {
        map.source = index;
      } else if (normalizedHeader.includes('date') || normalizedHeader.includes('time')) {
        map.timestamp = index;
      } else if (normalizedHeader.includes('tag')) {
        map.tags = index;
      } else if (normalizedHeader.includes('competitor') || normalizedHeader.includes('comp')) {
        map.competitor = index;
      }
    });

    return map;
  }

  private parseCSVLine(line: string): string[] {
    const result: string[] = [];
    let current = '';
    let inQuotes = false;
    
    for (let i = 0; i < line.length; i++) {
      const char = line[i];
      
      if (char === '"') {
        inQuotes = !inQuotes;
      } else if (char === ',' && !inQuotes) {
        result.push(current.trim());
        current = '';
      } else {
        current += char;
      }
    }
    
    result.push(current.trim());
    return result;
  }

  private transformCSVRowToScrapedData(
    values: string[], 
    headerMap: Record<string, number>, 
    options: DataUpdateOptions
  ): ScrapedData {
    const getValue = (field: string): string => {
      const index = headerMap[field];
      return index !== undefined ? values[index]?.replace(/"/g, '') || '' : '';
    };

    const id = this.generateId();
    const timestamp = getValue('timestamp') || new Date().toISOString();
    
    return {
      id,
      title: getValue('title') || 'Untitled',
      description: getValue('description') || '',
      url: getValue('url') || '',
      category: options.category || getValue('category') || 'Graphic Templates',
      price: this.parseNumber(getValue('price')),
      rating: this.parseNumber(getValue('rating')),
      timestamp: this.normalizeTimestamp(timestamp),
      source: options.source || getValue('source') || 'Envato',
      competitor: options.competitor || getValue('competitor'),
      tags: this.parseTags(getValue('tags')),
      metadata: this.extractMetadata(values, headerMap)
    };
  }

  private validateScrapedData(item: ScrapedData): boolean {
    return !!(item.id && item.title && item.category && item.source);
  }

  private parseNumber(value: string): number | undefined {
    if (!value) return undefined;
    const cleaned = value.replace(/[^0-9.-]/g, '');
    const num = parseFloat(cleaned);
    return isNaN(num) ? undefined : num;
  }

  private normalizeTimestamp(timestamp: string): string {
    if (!timestamp) return new Date().toISOString();
    
    try {
      return new Date(timestamp).toISOString();
    } catch {
      return new Date().toISOString();
    }
  }

  private parseTags(tagsString: string): string[] {
    if (!tagsString) return [];
    return tagsString.split(/[,;|]/).map(tag => tag.trim()).filter(tag => tag.length > 0);
  }

  private extractMetadata(values: string[], headerMap: Record<string, number>): Record<string, any> {
    const metadata: Record<string, any> = {};
    const standardFields = new Set(['title', 'description', 'url', 'category', 'price', 'rating', 'timestamp', 'source', 'tags', 'competitor']);
    
    Object.entries(headerMap).forEach(([field, index]) => {
      if (!standardFields.has(field) && values[index]) {
        metadata[field] = values[index];
      }
    });
    
    return metadata;
  }

  private generateId(): string {
    return `item_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  // Data management methods
  importData(csvContent: string, source: DataSource, options: DataUpdateOptions): CSVImportResult {
    const result = this.parseCSV(csvContent, options);
    
    if (result.success) {
      this.updateData(result.data, options);
      this.addDataSource(source);
      this.updateCategoriesAndCompetitors();
      this.saveToStorage();
    }
    
    return result;
  }

  private updateData(newData: ScrapedData[], options: DataUpdateOptions): void {
    switch (options.mergeStrategy) {
      case 'replace':
        if (options.category) {
          this.data = this.data.filter(item => item.category !== options.category);
        } else if (options.competitor) {
          this.data = this.data.filter(item => item.competitor !== options.competitor);
        } else {
          this.data = [];
        }
        this.data.push(...newData);
        break;
        
      case 'append':
        this.data.push(...newData);
        break;
        
      case 'update':
        newData.forEach(newItem => {
          const existingIndex = this.data.findIndex(item => 
            item.title === newItem.title && 
            item.source === newItem.source &&
            item.category === newItem.category
          );
          
          if (existingIndex >= 0) {
            this.data[existingIndex] = newItem;
          } else {
            this.data.push(newItem);
          }
        });
        break;
    }
  }

  private addDataSource(source: DataSource): void {
    const existingIndex = this.dataSources.findIndex(ds => ds.filename === source.filename);
    if (existingIndex >= 0) {
      this.dataSources[existingIndex] = source;
    } else {
      this.dataSources.push(source);
    }
  }

  private updateCategoriesAndCompetitors(): void {
    // Update categories
    const categoryMap = new Map<string, { count: number; totalPrice: number; lastUpdated: string }>();
    
    this.data.forEach(item => {
      const existing = categoryMap.get(item.category) || { count: 0, totalPrice: 0, lastUpdated: item.timestamp };
      categoryMap.set(item.category, {
        count: existing.count + 1,
        totalPrice: existing.totalPrice + (item.price || 0),
        lastUpdated: item.timestamp > existing.lastUpdated ? item.timestamp : existing.lastUpdated
      });
    });

    this.categories = Array.from(categoryMap.entries()).map(([name, stats], index) => ({
      id: `cat_${index + 1}`,
      name,
      description: this.getCategoryDescription(name),
      itemCount: stats.count,
      averagePrice: stats.count > 0 ? stats.totalPrice / stats.count : 0,
      lastUpdated: stats.lastUpdated,
      color: this.getCategoryColor(name)
    }));

    // Update competitors
    const competitorMap = new Map<string, { count: number; totalPrice: number; totalRating: number; ratingCount: number; lastUpdated: string }>();
    
    this.data.forEach(item => {
      if (item.competitor) {
        const existing = competitorMap.get(item.competitor) || { 
          count: 0, totalPrice: 0, totalRating: 0, ratingCount: 0, lastUpdated: item.timestamp 
        };
        competitorMap.set(item.competitor, {
          count: existing.count + 1,
          totalPrice: existing.totalPrice + (item.price || 0),
          totalRating: existing.totalRating + (item.rating || 0),
          ratingCount: existing.ratingCount + (item.rating ? 1 : 0),
          lastUpdated: item.timestamp > existing.lastUpdated ? item.timestamp : existing.lastUpdated
        });
      }
    });

    this.competitors = Array.from(competitorMap.entries()).map(([name, stats], index) => ({
      id: `comp_${index + 1}`,
      name,
      website: this.getCompetitorWebsite(name),
      itemCount: stats.count,
      averagePrice: stats.count > 0 ? stats.totalPrice / stats.count : 0,
      averageRating: stats.ratingCount > 0 ? stats.totalRating / stats.ratingCount : 0,
      lastUpdated: stats.lastUpdated,
      color: this.getCompetitorColor(name)
    }));
  }

  private getCategoryDescription(name: string): string {
    const descriptions: Record<string, string> = {
      'Graphic Templates': 'Design templates and graphics from Envato and other sources',
      'Electronics': 'Consumer electronics and tech gadgets',
      'Fashion': 'Clothing and fashion accessories',
      'Home & Garden': 'Home improvement and garden products',
      'Sports & Outdoors': 'Athletic gear and outdoor equipment',
      'Food & Beverage': 'Food products and beverages'
    };
    return descriptions[name] || `${name} category items`;
  }

  private getCategoryColor(name: string): string {
    const colors = ['#3B82F6', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6', '#06B6D4', '#84CC16'];
    const hash = name.split('').reduce((acc, char) => acc + char.charCodeAt(0), 0);
    return colors[hash % colors.length];
  }

  private getCompetitorWebsite(name: string): string {
    const websites: Record<string, string> = {
      'Envato': 'envato.com',
      'Creative Market': 'creativemarket.com',
      'Shutterstock': 'shutterstock.com',
      'Adobe Stock': 'stock.adobe.com'
    };
    return websites[name] || `${name.toLowerCase().replace(/\s+/g, '')}.com`;
  }

  private getCompetitorColor(name: string): string {
    return this.getCategoryColor(name);
  }

  // Storage methods
  private saveToStorage(): void {
    try {
      localStorage.setItem(STORAGE_KEYS.SCRAPED_DATA, JSON.stringify(this.data));
      localStorage.setItem(STORAGE_KEYS.DATA_SOURCES, JSON.stringify(this.dataSources));
      localStorage.setItem(STORAGE_KEYS.CATEGORIES, JSON.stringify(this.categories));
      localStorage.setItem(STORAGE_KEYS.COMPETITORS, JSON.stringify(this.competitors));
      localStorage.setItem(STORAGE_KEYS.LAST_UPDATE, new Date().toISOString());
    } catch (error) {
      console.error('Failed to save data to storage:', error);
    }
  }

  private loadFromStorage(): void {
    try {
      const data = localStorage.getItem(STORAGE_KEYS.SCRAPED_DATA);
      const sources = localStorage.getItem(STORAGE_KEYS.DATA_SOURCES);
      const categories = localStorage.getItem(STORAGE_KEYS.CATEGORIES);
      const competitors = localStorage.getItem(STORAGE_KEYS.COMPETITORS);

      if (data) this.data = JSON.parse(data);
      if (sources) this.dataSources = JSON.parse(sources);
      if (categories) this.categories = JSON.parse(categories);
      if (competitors) this.competitors = JSON.parse(competitors);
    } catch (error) {
      console.error('Failed to load data from storage:', error);
    }
  }

  // Public getters
  getAllData(): ScrapedData[] {
    return [...this.data];
  }

  getDataSources(): DataSource[] {
    return [...this.dataSources];
  }

  getCategories(): CategoryData[] {
    return [...this.categories];
  }

  getCompetitors(): CompetitorData[] {
    return [...this.competitors];
  }

  getDataByCategory(category: string): ScrapedData[] {
    return this.data.filter(item => item.category === category);
  }

  getDataByCompetitor(competitor: string): ScrapedData[] {
    return this.data.filter(item => item.competitor === competitor);
  }

  getLastUpdateTime(): string | null {
    return localStorage.getItem(STORAGE_KEYS.LAST_UPDATE);
  }

  // Clear all data
  clearAllData(): void {
    this.data = [];
    this.dataSources = [];
    this.categories = [];
    this.competitors = [];
    Object.values(STORAGE_KEYS).forEach(key => localStorage.removeItem(key));
  }

  // Dynamic category management
  addCategory(name: string, description?: string): CategoryData {
    const existingCategory = this.categories.find(cat => cat.name === name);
    if (existingCategory) {
      throw new Error(`Category "${name}" already exists`);
    }

    const newCategory: CategoryData = {
      id: `cat_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      name,
      description: description || `${name} category items`,
      itemCount: 0,
      averagePrice: 0,
      lastUpdated: new Date().toISOString(),
      color: this.getCategoryColor(name)
    };

    this.categories.push(newCategory);
    this.saveToStorage();
    return newCategory;
  }

  removeCategory(categoryId: string): boolean {
    const categoryIndex = this.categories.findIndex(cat => cat.id === categoryId);
    if (categoryIndex === -1) {
      return false;
    }

    const categoryName = this.categories[categoryIndex].name;

    // Remove the category
    this.categories.splice(categoryIndex, 1);

    // Remove all data items in this category
    this.data = this.data.filter(item => item.category !== categoryName);

    this.saveToStorage();
    return true;
  }

  updateCategory(categoryId: string, updates: Partial<Pick<CategoryData, 'name' | 'description'>>): boolean {
    const categoryIndex = this.categories.findIndex(cat => cat.id === categoryId);
    if (categoryIndex === -1) {
      return false;
    }

    const oldName = this.categories[categoryIndex].name;

    // Update category
    this.categories[categoryIndex] = {
      ...this.categories[categoryIndex],
      ...updates,
      lastUpdated: new Date().toISOString()
    };

    // If name changed, update all data items
    if (updates.name && updates.name !== oldName) {
      this.data.forEach(item => {
        if (item.category === oldName) {
          item.category = updates.name!;
        }
      });
    }

    this.saveToStorage();
    return true;
  }

  // Dynamic competitor management
  addCompetitor(name: string, website?: string): CompetitorData {
    const existingCompetitor = this.competitors.find(comp => comp.name === name);
    if (existingCompetitor) {
      throw new Error(`Competitor "${name}" already exists`);
    }

    const newCompetitor: CompetitorData = {
      id: `comp_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      name,
      website: website || this.getCompetitorWebsite(name),
      itemCount: 0,
      averagePrice: 0,
      averageRating: 0,
      lastUpdated: new Date().toISOString(),
      color: this.getCompetitorColor(name)
    };

    this.competitors.push(newCompetitor);
    this.saveToStorage();
    return newCompetitor;
  }

  removeCompetitor(competitorId: string): boolean {
    const competitorIndex = this.competitors.findIndex(comp => comp.id === competitorId);
    if (competitorIndex === -1) {
      return false;
    }

    const competitorName = this.competitors[competitorIndex].name;

    // Remove the competitor
    this.competitors.splice(competitorIndex, 1);

    // Remove competitor reference from data items (but keep the items)
    this.data.forEach(item => {
      if (item.competitor === competitorName) {
        delete item.competitor;
      }
    });

    this.saveToStorage();
    return true;
  }

  updateCompetitor(competitorId: string, updates: Partial<Pick<CompetitorData, 'name' | 'website'>>): boolean {
    const competitorIndex = this.competitors.findIndex(comp => comp.id === competitorId);
    if (competitorIndex === -1) {
      return false;
    }

    const oldName = this.competitors[competitorIndex].name;

    // Update competitor
    this.competitors[competitorIndex] = {
      ...this.competitors[competitorIndex],
      ...updates,
      lastUpdated: new Date().toISOString()
    };

    // If name changed, update all data items
    if (updates.name && updates.name !== oldName) {
      this.data.forEach(item => {
        if (item.competitor === oldName) {
          item.competitor = updates.name!;
        }
      });
    }

    this.saveToStorage();
    return true;
  }
}

// Export singleton instance
export const dataManager = DataManager.getInstance();
