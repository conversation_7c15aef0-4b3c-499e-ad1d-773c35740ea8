import { saveAs } from 'file-saver';
import * as XLSX from 'xlsx';
import jsPDF from 'jspdf';
import autoTable from 'jspdf-autotable';
import { ScrapedData, ExportOptions } from '../types';

export const exportToCSV = (data: ScrapedData[], filename: string = 'scraped_data.csv') => {
  const csvContent = [
    ['ID', 'Title', 'Category', 'Price', 'Rating', 'Source', 'Timestamp'],
    ...data.map(item => [
      item.id,
      item.title,
      item.category,
      item.price?.toString() || '',
      item.rating?.toString() || '',
      item.source,
      item.timestamp
    ])
  ].map(row => row.join(',')).join('\n');

  const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
  saveAs(blob, filename);
};

export const exportToExcel = (data: ScrapedData[], filename: string = 'scraped_data.xlsx') => {
  const worksheet = XLSX.utils.json_to_sheet(data.map(item => ({
    ID: item.id,
    Title: item.title,
    Description: item.description,
    Category: item.category,
    Price: item.price,
    Rating: item.rating,
    Source: item.source,
    Timestamp: item.timestamp,
    Tags: item.tags.join(', ')
  })));

  const workbook = XLSX.utils.book_new();
  XLSX.utils.book_append_sheet(workbook, worksheet, 'Scraped Data');
  XLSX.writeFile(workbook, filename);
};

export const exportToPDF = (data: ScrapedData[], filename: string = 'scraped_data.pdf') => {
  const doc = new jsPDF();
  
  doc.setFontSize(20);
  doc.text('Scraped Data Report', 14, 22);
  
  doc.setFontSize(12);
  doc.text(`Generated on: ${new Date().toLocaleDateString()}`, 14, 32);
  doc.text(`Total Items: ${data.length}`, 14, 40);

  const tableData = data.map(item => [
    item.id,
    item.title.substring(0, 30) + (item.title.length > 30 ? '...' : ''),
    item.category,
    item.price ? `$${item.price}` : '',
    item.rating?.toString() || '',
    item.source
  ]);

  autoTable(doc, {
    head: [['ID', 'Title', 'Category', 'Price', 'Rating', 'Source']],
    body: tableData,
    startY: 50,
    styles: { fontSize: 8 },
    headStyles: { fillColor: [59, 130, 246] }
  });

  doc.save(filename);
};

export const handleExport = (data: ScrapedData[], options: ExportOptions) => {
  const timestamp = new Date().toISOString().split('T')[0];
  const filename = `scraped_data_${timestamp}`;

  switch (options.format) {
    case 'csv':
      exportToCSV(data, `${filename}.csv`);
      break;
    case 'excel':
      exportToExcel(data, `${filename}.xlsx`);
      break;
    case 'pdf':
      exportToPDF(data, `${filename}.pdf`);
      break;
    default:
      console.error('Unsupported export format');
  }
};