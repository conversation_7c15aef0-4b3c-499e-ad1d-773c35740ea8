import { 
  ScrapedData, 
  CategorySnapshot, 
  RankingComparison, 
  RankingChangeItem, 
  FilenamePattern,
  RankingChange 
} from '../types';

export class RankingAnalyzer {
  
  /**
   * Parse filename pattern: csvtitle_category_day_month_year.csv
   */
  static parseFilename(filename: string): FilenamePattern | null {
    // Remove .csv extension
    const nameWithoutExt = filename.replace(/\.csv$/i, '');
    
    // Split by underscore
    const parts = nameWithoutExt.split('_');
    
    if (parts.length < 5) {
      return null; // Not matching the expected pattern
    }
    
    // Extract parts (last 3 are day, month, year)
    const year = parseInt(parts[parts.length - 1]);
    const month = parseInt(parts[parts.length - 2]);
    const day = parseInt(parts[parts.length - 3]);
    
    // Category is the second-to-last part before date
    const category = parts[parts.length - 4];
    
    // CSV title is everything before category and date
    const csvTitle = parts.slice(0, parts.length - 4).join('_');
    
    // Validate date components
    if (isNaN(day) || isNaN(month) || isNaN(year) || 
        day < 1 || day > 31 || month < 1 || month > 12 || year < 2000) {
      return null;
    }
    
    const fullDate = new Date(year, month - 1, day); // month is 0-indexed in Date
    
    return {
      csvTitle,
      category,
      day,
      month,
      year,
      fullDate
    };
  }

  /**
   * Create a category snapshot from imported data
   */
  static createSnapshot(
    data: ScrapedData[], 
    filename: string, 
    category: string,
    timestamp: string
  ): CategorySnapshot {
    const filenamePattern = this.parseFilename(filename);
    const date = filenamePattern?.fullDate || new Date();
    
    // Sort items by position for consistent ranking
    const sortedItems = data
      .filter(item => item.category === category)
      .sort((a, b) => (a.position || 0) - (b.position || 0));
    
    return {
      id: `snapshot_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      category,
      filename,
      timestamp,
      date,
      itemCount: sortedItems.length,
      items: sortedItems
    };
  }

  /**
   * Compare two category snapshots and calculate ranking changes
   */
  static compareSnapshots(
    currentSnapshot: CategorySnapshot,
    previousSnapshot?: CategorySnapshot
  ): RankingComparison {
    const changes: RankingChangeItem[] = [];
    
    if (!previousSnapshot) {
      // First snapshot - all items are new
      currentSnapshot.items.forEach((item, index) => {
        if (item.link) {
          changes.push({
            link: item.link,
            title: item.title,
            author: item.author,
            currentPosition: index + 1,
            change: 0,
            changeType: 'new'
          });
        }
      });
    } else {
      // Create maps for efficient lookup
      const previousMap = new Map<string, { item: ScrapedData; position: number }>();
      const currentMap = new Map<string, { item: ScrapedData; position: number }>();
      
      previousSnapshot.items.forEach((item, index) => {
        if (item.link) {
          previousMap.set(item.link, { item, position: index + 1 });
        }
      });
      
      currentSnapshot.items.forEach((item, index) => {
        if (item.link) {
          currentMap.set(item.link, { item, position: index + 1 });
        }
      });
      
      // Analyze current items
      currentSnapshot.items.forEach((currentItem, index) => {
        if (!currentItem.link) return;
        
        const currentPosition = index + 1;
        const previousData = previousMap.get(currentItem.link);
        
        if (previousData) {
          // Item existed in previous snapshot
          const previousPosition = previousData.position;
          const change = previousPosition - currentPosition; // Positive = moved up
          
          let changeType: 'up' | 'down' | 'same';
          if (change > 0) changeType = 'up';
          else if (change < 0) changeType = 'down';
          else changeType = 'same';
          
          changes.push({
            link: currentItem.link,
            title: currentItem.title,
            author: currentItem.author,
            previousPosition,
            currentPosition,
            change,
            changeType
          });
        } else {
          // New item
          changes.push({
            link: currentItem.link,
            title: currentItem.title,
            author: currentItem.author,
            currentPosition,
            change: 0,
            changeType: 'new'
          });
        }
      });
      
      // Find removed items
      previousSnapshot.items.forEach((previousItem, index) => {
        if (!previousItem.link) return;
        
        if (!currentMap.has(previousItem.link)) {
          changes.push({
            link: previousItem.link,
            title: previousItem.title,
            author: previousItem.author,
            previousPosition: index + 1,
            currentPosition: -1,
            change: 0,
            changeType: 'removed'
          });
        }
      });
    }
    
    // Calculate summary statistics
    const summary = {
      totalItems: currentSnapshot.itemCount,
      movedUp: changes.filter(c => c.changeType === 'up').length,
      movedDown: changes.filter(c => c.changeType === 'down').length,
      stayedSame: changes.filter(c => c.changeType === 'same').length,
      newItems: changes.filter(c => c.changeType === 'new').length,
      removedItems: changes.filter(c => c.changeType === 'removed').length
    };
    
    return {
      category: currentSnapshot.category,
      currentSnapshot,
      previousSnapshot,
      changes,
      summary
    };
  }

  /**
   * Apply ranking changes to scraped data items
   */
  static applyRankingChanges(
    data: ScrapedData[],
    comparison: RankingComparison
  ): ScrapedData[] {
    const changeMap = new Map<string, RankingChangeItem>();
    comparison.changes.forEach(change => {
      changeMap.set(change.link, change);
    });
    
    return data.map(item => {
      if (!item.link) return item;
      
      const change = changeMap.get(item.link);
      if (!change) return item;
      
      const rankingChange: RankingChange = {
        previousPosition: change.previousPosition,
        currentPosition: change.currentPosition,
        change: change.change,
        changeType: change.changeType,
        previousSnapshot: comparison.previousSnapshot?.timestamp
      };
      
      return {
        ...item,
        position: change.currentPosition,
        rankingChange
      };
    });
  }

  /**
   * Get the most recent snapshot for a category
   */
  static getMostRecentSnapshot(
    snapshots: CategorySnapshot[],
    category: string
  ): CategorySnapshot | undefined {
    return snapshots
      .filter(s => s.category === category)
      .sort((a, b) => b.date.getTime() - a.date.getTime())[0];
  }

  /**
   * Get all snapshots for a category sorted by date
   */
  static getSnapshotsForCategory(
    snapshots: CategorySnapshot[],
    category: string
  ): CategorySnapshot[] {
    return snapshots
      .filter(s => s.category === category)
      .sort((a, b) => a.date.getTime() - b.date.getTime());
  }

  /**
   * Validate if filename matches the expected pattern
   */
  static isValidFilenamePattern(filename: string): boolean {
    return this.parseFilename(filename) !== null;
  }

  /**
   * Extract category from filename
   */
  static getCategoryFromFilename(filename: string): string | null {
    const pattern = this.parseFilename(filename);
    return pattern?.category || null;
  }
}
