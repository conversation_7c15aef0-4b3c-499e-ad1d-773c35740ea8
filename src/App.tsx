import React, { useState, useEffect, useMemo } from 'react';
import Sidebar from './components/Layout/Sidebar';
import Header from './components/Layout/Header';
import MetricsCard from './components/Metrics/MetricsCard';
import LineChartComponent from './components/Charts/LineChart';
import BarChartComponent from './components/Charts/BarChart';
import PieChartComponent from './components/Charts/PieChart';
import DataTable from './components/Data/DataTable';
import FilterPanel from './components/Filters/FilterPanel';
import ExportPanel from './components/Export/ExportPanel';
import CategoryOverview from './components/Categories/CategoryOverview';
import CategoryDetail from './components/Categories/CategoryDetail';
import CompetitorOverview from './components/Competitors/CompetitorOverview';
import CompetitorDetail from './components/Competitors/CompetitorDetail';
import { ScrapedData, FilterOptions } from './types';
import { mockScrapedData, mockMetrics, mockChartData, mockCategoryData, mockCompetitorData } from './data/mockData';

function App() {
  const [activeTab, setActiveTab] = useState('overview');
  const [data, setData] = useState<ScrapedData[]>(mockScrapedData);
  const [isLoading, setIsLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [filters, setFilters] = useState<FilterOptions>({
    category: '',
    source: '',
    competitor: '',
    dateRange: { start: null, end: null },
    priceRange: { min: 0, max: 0 },
    search: ''
  });

  // Sync search term with filters
  useEffect(() => {
    setFilters(prev => ({ ...prev, search: searchTerm }));
  }, [searchTerm]);

  // Get unique categories, sources, and competitors for filters
  const categories = useMemo(() => 
    Array.from(new Set(data.map(item => item.category))).sort(),
    [data]
  );

  const sources = useMemo(() => 
    Array.from(new Set(data.map(item => item.source))).sort(),
    [data]
  );

  const competitors = useMemo(() => 
    Array.from(new Set(data.map(item => item.competitor).filter(Boolean))).sort(),
    [data]
  );

  // Filter data based on current filters
  const filteredData = useMemo(() => {
    return data.filter(item => {
      // Search filter
      if (filters.search && !item.title.toLowerCase().includes(filters.search.toLowerCase()) &&
          !item.description.toLowerCase().includes(filters.search.toLowerCase())) {
        return false;
      }

      // Category filter
      if (filters.category && item.category !== filters.category) {
        return false;
      }

      // Competitor filter
      if (filters.competitor && item.competitor !== filters.competitor) {
        return false;
      }

      // Source filter
      if (filters.source && item.source !== filters.source) {
        return false;
      }

      // Date range filter
      if (filters.dateRange.start || filters.dateRange.end) {
        const itemDate = new Date(item.timestamp);
        if (filters.dateRange.start && itemDate < filters.dateRange.start) {
          return false;
        }
        if (filters.dateRange.end && itemDate > filters.dateRange.end) {
          return false;
        }
      }

      // Price range filter
      if (filters.priceRange.min > 0 || filters.priceRange.max > 0) {
        if (!item.price) return false;
        if (filters.priceRange.min > 0 && item.price < filters.priceRange.min) {
          return false;
        }
        if (filters.priceRange.max > 0 && item.price > filters.priceRange.max) {
          return false;
        }
      }

      return true;
    });
  }, [data, filters]);

  const handleRefresh = async () => {
    setIsLoading(true);
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000));
    setIsLoading(false);
  };

  const handleItemClick = (item: ScrapedData) => {
    console.log('Item clicked:', item);
    // You can implement a modal or detail view here
  };

  const renderContent = () => {
    switch (activeTab) {
      case 'overview':
        return (
          <div className="space-y-6">
            {/* Metrics */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {mockMetrics.map((metric, index) => (
                <MetricsCard key={index} metric={metric} />
              ))}
            </div>

            {/* Charts */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <LineChartComponent 
                data={mockChartData} 
                title="Scraping Activity Over Time"
                dataKey="value"
                height={300}
              />
              <PieChartComponent 
                data={mockCategoryData} 
                title="Data by Category"
                dataKey="value"
                height={300}
              />
            </div>

            {/* Recent Data Table */}
            <div>
              <h2 className="text-xl font-semibold text-gray-900 mb-4">Recent Data</h2>
              <DataTable 
                data={filteredData.slice(0, 5)} 
                onItemClick={handleItemClick}
              />
            </div>
          </div>
        );

      case 'data':
        return (
          <div className="space-y-6">
            <div className="flex justify-between items-center">
              <h2 className="text-xl font-semibold text-gray-900">All Scraped Data</h2>
              <p className="text-sm text-gray-600">
                Showing {filteredData.length} of {data.length} items
              </p>
            </div>
            <DataTable data={filteredData} onItemClick={handleItemClick} />
          </div>
        );

      case 'analytics':
        return (
          <div className="space-y-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">Analytics Dashboard</h2>
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <BarChartComponent 
                data={mockCategoryData} 
                title="Items by Category"
                dataKey="value"
                height={300}
              />
              <BarChartComponent 
                data={mockCompetitorData} 
                title="Items by Competitor"
                dataKey="value"
                height={300}
              />
            </div>
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <PieChartComponent 
                data={mockCategoryData} 
                title="Category Distribution"
                dataKey="value"
                height={300}
              />
              <PieChartComponent 
                data={mockCompetitorData} 
                title="Competitor Distribution"
                dataKey="value"
                height={300}
              />
            </div>
          </div>
        );

      case 'filters':
        return (
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <div className="lg:col-span-1">
              <FilterPanel 
                filters={filters}
                onFiltersChange={setFilters}
                categories={categories}
                sources={sources}
                competitors={competitors}
              />
            </div>
            <div className="lg:col-span-2">
              <div className="space-y-6">
                <div className="flex justify-between items-center">
                  <h2 className="text-xl font-semibold text-gray-900">Filtered Results</h2>
                  <p className="text-sm text-gray-600">
                    {filteredData.length} items match your filters
                  </p>
                </div>
                <DataTable data={filteredData} onItemClick={handleItemClick} />
              </div>
            </div>
          </div>
        );

      case 'exports':
        return (
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <div className="lg:col-span-1">
              <ExportPanel data={data} filteredData={filteredData} />
            </div>
            <div className="lg:col-span-2">
              <div className="space-y-6">
                <h2 className="text-xl font-semibold text-gray-900">Export Preview</h2>
                <DataTable data={filteredData.slice(0, 10)} onItemClick={handleItemClick} />
              </div>
            </div>
          </div>
        );

      // Categories
      case 'categories-overview':
        return <CategoryOverview />;
      
      case 'categories-electronics':
        return <CategoryDetail categoryName="Electronics" onBack={() => setActiveTab('categories-overview')} />;
      
      case 'categories-sports':
        return <CategoryDetail categoryName="Sports & Outdoors" onBack={() => setActiveTab('categories-overview')} />;
      
      case 'categories-food':
        return <CategoryDetail categoryName="Food & Beverage" onBack={() => setActiveTab('categories-overview')} />;
      
      case 'categories-fashion':
        return <CategoryDetail categoryName="Fashion" onBack={() => setActiveTab('categories-overview')} />;

      // Competitors
      case 'competitors-overview':
        return <CompetitorOverview />;
      
      case 'competitors-techcorp':
        return <CompetitorDetail competitorName="TechCorp" onBack={() => setActiveTab('competitors-overview')} />;
      
      case 'competitors-sportspro':
        return <CompetitorDetail competitorName="SportsPro" onBack={() => setActiveTab('competitors-overview')} />;
      
      case 'competitors-gametech':
        return <CompetitorDetail competitorName="GameTech" onBack={() => setActiveTab('competitors-overview')} />;
      
      case 'competitors-fitnesstech':
        return <CompetitorDetail competitorName="FitnessTech" onBack={() => setActiveTab('competitors-overview')} />;

      case 'settings':
        return (
          <div className="space-y-6">
            <h2 className="text-xl font-semibold text-gray-900">Settings</h2>
            <div className="bg-white rounded-lg shadow-md p-6">
              <p className="text-gray-600">Settings panel coming soon...</p>
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 flex">
      {/* Sidebar */}
      <Sidebar activeTab={activeTab} onTabChange={setActiveTab} />
      
      {/* Main Content */}
      <div className="flex-1 flex flex-col">
        {/* Header */}
        <Header 
          onRefresh={handleRefresh}
          isLoading={isLoading}
          searchTerm={searchTerm}
          onSearchChange={setSearchTerm}
        />
        
        {/* Content */}
        <main className="flex-1 p-6 overflow-auto">
          {renderContent()}
        </main>
      </div>
    </div>
  );
}

export default App;