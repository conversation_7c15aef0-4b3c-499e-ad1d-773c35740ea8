import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import {
  ScrapedData,
  CategoryData,
  CompetitorData,
  MetricData,
  ChartData,
  DataSource,
  CategorySnapshot,
  RankingComparison,
  Subcategory,
  CompetitorSubcategory
} from '../types';
import { dataManager } from '../utils/dataManager';
import { mockMetrics, mockChartData } from '../data/mockData';

interface DataContextType {
  // Data
  scrapedData: ScrapedData[];
  categories: CategoryData[];
  competitors: CompetitorData[];
  dataSources: DataSource[];

  // Computed data
  metrics: MetricData[];
  categoryChartData: ChartData[];
  competitorChartData: ChartData[];
  chartData: ChartData[];

  // Ranking data
  categorySnapshots: CategorySnapshot[];
  rankingComparisons: RankingComparison[];

  // State
  isLoading: boolean;
  lastUpdate: string | null;
  
  // Actions
  refreshData: () => void;
  getDataByCategory: (category: string) => ScrapedData[];
  getDataByCompetitor: (competitor: string) => ScrapedData[];
  getDataBySource: (sourceId: string) => ScrapedData[];
  clearAllData: () => void;
  removeDataSource: (sourceId: string) => Promise<boolean>;
  updateDataSource: (sourceId: string, updates: Partial<DataSource>) => Promise<boolean>;

  // Dynamic management
  addCategory: (name: string, description?: string) => Promise<CategoryData>;
  removeCategory: (categoryId: string) => Promise<boolean>;
  updateCategory: (categoryId: string, updates: Partial<Pick<CategoryData, 'name' | 'description'>>) => Promise<boolean>;
  addCompetitor: (name: string, website?: string) => Promise<CompetitorData>;
  removeCompetitor: (competitorId: string) => Promise<boolean>;
  updateCompetitor: (competitorId: string, updates: Partial<Pick<CompetitorData, 'name' | 'website'>>) => Promise<boolean>;

  // Subcategory management
  addSubcategory: (categoryId: string, name: string, description?: string) => Promise<Subcategory>;
  removeSubcategory: (subcategoryId: string) => Promise<boolean>;
  updateSubcategory: (subcategoryId: string, updates: Partial<Pick<Subcategory, 'name' | 'description'>>) => Promise<boolean>;
  addCompetitorSubcategory: (competitorId: string, name: string, description?: string) => Promise<CompetitorSubcategory>;
  removeCompetitorSubcategory: (subcategoryId: string) => Promise<boolean>;
  updateCompetitorSubcategory: (subcategoryId: string, updates: Partial<Pick<CompetitorSubcategory, 'name' | 'description'>>) => Promise<boolean>;

  // Ranking methods
  getLatestRankingComparison: (category: string) => RankingComparison | undefined;
  getRankingHistory: (category: string) => RankingComparison[];
}

const DataContext = createContext<DataContextType | undefined>(undefined);

export const useData = () => {
  const context = useContext(DataContext);
  if (context === undefined) {
    throw new Error('useData must be used within a DataProvider');
  }
  return context;
};

interface DataProviderProps {
  children: ReactNode;
}

export const DataProvider: React.FC<DataProviderProps> = ({ children }) => {
  const [scrapedData, setScrapedData] = useState<ScrapedData[]>([]);
  const [categories, setCategories] = useState<CategoryData[]>([]);
  const [competitors, setCompetitors] = useState<CompetitorData[]>([]);
  const [dataSources, setDataSources] = useState<DataSource[]>([]);
  const [categorySnapshots, setCategorySnapshots] = useState<CategorySnapshot[]>([]);
  const [rankingComparisons, setRankingComparisons] = useState<RankingComparison[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [lastUpdate, setLastUpdate] = useState<string | null>(null);

  const refreshData = () => {
    setIsLoading(true);
    try {
      const data = dataManager.getAllData();
      const cats = dataManager.getCategories();
      const comps = dataManager.getCompetitors();
      const sources = dataManager.getDataSources();
      const snapshots = dataManager.getCategorySnapshots();
      const comparisons = dataManager.getRankingComparisons();
      const lastUpdateTime = dataManager.getLastUpdateTime();

      setScrapedData(data);
      setCategories(cats);
      setCompetitors(comps);
      setDataSources(sources);
      setCategorySnapshots(snapshots);
      setRankingComparisons(comparisons);
      setLastUpdate(lastUpdateTime);
    } catch (error) {
      console.error('Error refreshing data:', error);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    refreshData();
  }, []);

  // Computed metrics based on real data
  const metrics: MetricData[] = React.useMemo(() => {
    if (scrapedData.length === 0) {
      return mockMetrics; // Fallback to mock data if no real data
    }

    const totalItems = scrapedData.length;
    const avgPrice = scrapedData.reduce((sum, item) => sum + (item.price || 0), 0) / totalItems;
    const uniqueSources = new Set(scrapedData.map(item => item.source)).size;
    
    // Calculate success rate based on items with complete data
    const completeItems = scrapedData.filter(item => 
      item.title && item.category && item.source && (item.price !== undefined || item.rating !== undefined)
    ).length;
    const successRate = (completeItems / totalItems) * 100;

    return [
      {
        label: 'Total Items Scraped',
        value: totalItems,
        change: 0, // TODO: Calculate based on previous data
        changeType: 'neutral',
        icon: 'Database'
      },
      {
        label: 'Average Price',
        value: avgPrice,
        change: 0, // TODO: Calculate based on previous data
        changeType: 'neutral',
        icon: 'DollarSign'
      },
      {
        label: 'Data Sources',
        value: uniqueSources,
        change: 0, // TODO: Calculate based on previous data
        changeType: 'neutral',
        icon: 'Globe'
      },
      {
        label: 'Data Completeness',
        value: successRate,
        change: 0, // TODO: Calculate based on previous data
        changeType: 'neutral',
        icon: 'TrendingUp'
      }
    ];
  }, [scrapedData]);

  // Category chart data
  const categoryChartData: ChartData[] = React.useMemo(() => {
    if (categories.length === 0) {
      return [];
    }

    return categories.map(cat => ({
      name: cat.name,
      value: cat.itemCount,
      category: cat.name
    }));
  }, [categories]);

  // Competitor chart data
  const competitorChartData: ChartData[] = React.useMemo(() => {
    if (competitors.length === 0) {
      return [];
    }

    return competitors.map(comp => ({
      name: comp.name,
      value: comp.itemCount,
      competitor: comp.name
    }));
  }, [competitors]);

  // Time-based chart data for line charts
  const chartData: ChartData[] = React.useMemo(() => {
    if (scrapedData.length === 0) {
      return mockChartData; // Fallback to mock data
    }

    // Group data by date
    const dateMap = new Map<string, number>();
    
    scrapedData.forEach(item => {
      const date = new Date(item.timestamp).toISOString().split('T')[0];
      dateMap.set(date, (dateMap.get(date) || 0) + 1);
    });

    // Convert to chart data format and sort by date
    return Array.from(dateMap.entries())
      .map(([date, count]) => ({
        name: date,
        value: count,
        date
      }))
      .sort((a, b) => a.date!.localeCompare(b.date!))
      .slice(-30); // Last 30 days
  }, [scrapedData]);

  const getDataByCategory = (category: string): ScrapedData[] => {
    return dataManager.getDataByCategory(category);
  };

  const getDataByCompetitor = (competitor: string): ScrapedData[] => {
    return dataManager.getDataByCompetitor(competitor);
  };

  const getDataBySource = (sourceId: string): ScrapedData[] => {
    return dataManager.getDataBySource(sourceId);
  };

  const clearAllData = () => {
    dataManager.clearAllData();
    refreshData();
  };

  const removeDataSource = async (sourceId: string): Promise<boolean> => {
    const result = dataManager.removeDataSource(sourceId);
    if (result) {
      refreshData();
    }
    return result;
  };

  const updateDataSource = async (sourceId: string, updates: Partial<DataSource>): Promise<boolean> => {
    const result = dataManager.updateDataSource(sourceId, updates);
    if (result) {
      refreshData();
    }
    return result;
  };

  // Dynamic management functions
  const addCategory = async (name: string, description?: string): Promise<CategoryData> => {
    try {
      const newCategory = dataManager.addCategory(name, description);
      refreshData();
      return newCategory;
    } catch (error) {
      throw error;
    }
  };

  const removeCategory = async (categoryId: string): Promise<boolean> => {
    const result = dataManager.removeCategory(categoryId);
    if (result) {
      refreshData();
    }
    return result;
  };

  const updateCategory = async (categoryId: string, updates: Partial<Pick<CategoryData, 'name' | 'description'>>): Promise<boolean> => {
    const result = dataManager.updateCategory(categoryId, updates);
    if (result) {
      refreshData();
    }
    return result;
  };

  const addCompetitor = async (name: string, website?: string): Promise<CompetitorData> => {
    try {
      const newCompetitor = dataManager.addCompetitor(name, website);
      refreshData();
      return newCompetitor;
    } catch (error) {
      throw error;
    }
  };

  const removeCompetitor = async (competitorId: string): Promise<boolean> => {
    const result = dataManager.removeCompetitor(competitorId);
    if (result) {
      refreshData();
    }
    return result;
  };

  const updateCompetitor = async (competitorId: string, updates: Partial<Pick<CompetitorData, 'name' | 'website'>>): Promise<boolean> => {
    const result = dataManager.updateCompetitor(competitorId, updates);
    if (result) {
      refreshData();
    }
    return result;
  };

  // Ranking methods
  const getLatestRankingComparison = (category: string): RankingComparison | undefined => {
    return dataManager.getLatestRankingComparison(category);
  };

  const getRankingHistory = (category: string): RankingComparison[] => {
    return dataManager.getRankingHistory(category);
  };

  // Subcategory management methods
  const addSubcategory = async (categoryId: string, name: string, description?: string): Promise<Subcategory> => {
    const subcategory: Subcategory = {
      id: `subcategory-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      name,
      description,
      categoryId,
      itemCount: 0,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    // TODO: Implement actual storage logic
    console.log('Adding subcategory:', subcategory);

    return subcategory;
  };

  const removeSubcategory = async (subcategoryId: string): Promise<boolean> => {
    // TODO: Implement actual removal logic
    console.log('Removing subcategory:', subcategoryId);
    return true;
  };

  const updateSubcategory = async (subcategoryId: string, updates: Partial<Pick<Subcategory, 'name' | 'description'>>): Promise<boolean> => {
    // TODO: Implement actual update logic
    console.log('Updating subcategory:', subcategoryId, updates);
    return true;
  };

  const addCompetitorSubcategory = async (competitorId: string, name: string, description?: string): Promise<CompetitorSubcategory> => {
    const subcategory: CompetitorSubcategory = {
      id: `comp-subcategory-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      name,
      description,
      competitorId,
      itemCount: 0,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    // TODO: Implement actual storage logic
    console.log('Adding competitor subcategory:', subcategory);

    return subcategory;
  };

  const removeCompetitorSubcategory = async (subcategoryId: string): Promise<boolean> => {
    // TODO: Implement actual removal logic
    console.log('Removing competitor subcategory:', subcategoryId);
    return true;
  };

  const updateCompetitorSubcategory = async (subcategoryId: string, updates: Partial<Pick<CompetitorSubcategory, 'name' | 'description'>>): Promise<boolean> => {
    // TODO: Implement actual update logic
    console.log('Updating competitor subcategory:', subcategoryId, updates);
    return true;
  };

  const value: DataContextType = {
    scrapedData,
    categories,
    competitors,
    dataSources,
    metrics,
    categoryChartData,
    competitorChartData,
    chartData,
    categorySnapshots,
    rankingComparisons,
    isLoading,
    lastUpdate,
    refreshData,
    getDataByCategory,
    getDataByCompetitor,
    getDataBySource,
    clearAllData,
    removeDataSource,
    updateDataSource,
    addCategory,
    removeCategory,
    updateCategory,
    addCompetitor,
    removeCompetitor,
    updateCompetitor,
    addSubcategory,
    removeSubcategory,
    updateSubcategory,
    addCompetitorSubcategory,
    removeCompetitorSubcategory,
    updateCompetitorSubcategory,
    getLatestRankingComparison,
    getRankingHistory
  };

  return (
    <DataContext.Provider value={value}>
      {children}
    </DataContext.Provider>
  );
};
