# 📁 CSV File Management System Guide

## 🎯 **Overview**

The dashboard now includes a comprehensive file management system that allows you to view, manage, and analyze all your uploaded CSV files with detailed tracking and control capabilities.

## 📍 **Accessing File Management**

### **Navigation Path:**
1. **Overview** → **All Data** → **"Manage Files"** button
2. Or from any data import section → **"Manage Files"** button

### **Quick Access:**
- Located next to the "Import" button in the All Data section
- Gray button with database icon for easy identification

## 🗂️ **File Management Features**

### **1. View Uploaded CSV Files**

**📋 File List Overview:**
- **Complete file inventory** with all uploaded CSV files
- **Filename display** with original upload names
- **Upload dates** with precise timestamps
- **Row counts** showing total data rows per file
- **File size estimates** for storage management
- **File type indicators** (Standard Data, Ranking Snapshot, Ranking Analysis)

**📊 Summary Statistics:**
- **Total Files**: Count of all uploaded CSV files
- **Ranking Snapshots**: Files used for ranking analysis
- **Ranking Comparisons**: Generated comparison analyses
- **Total Rows**: Aggregate data across all files

### **2. File Management Operations**

**🗑️ Individual File Deletion:**
- **Single file removal** with confirmation dialog
- **Associated data cleanup** (removes all related data items)
- **Ranking data cleanup** (removes snapshots and comparisons)
- **Safe deletion** with detailed impact preview

**📦 Bulk Operations:**
- **Multi-select functionality** with checkboxes
- **Bulk deletion** for multiple files at once
- **Select all/none** toggle for convenience
- **Batch processing** with progress indication

**👁️ Detailed File View:**
- **Comprehensive file analysis** with statistics
- **Data breakdown** by categories, authors, and content
- **Ranking analysis details** for chronological files
- **Content statistics** (pricing, ratings, new items)

### **3. Data Source Tracking**

**🔍 Source Identification:**
- **Unique source IDs** for each uploaded file
- **Metadata tracking** linking data items to source files
- **Category association** from filename patterns or manual assignment
- **Upload history** with complete audit trail

**📈 Ranking File Tracking:**
- **Chronological pattern detection** (`csvtitle_category_day_month_year.csv`)
- **Snapshot creation** for ranking analysis files
- **Comparison generation** between consecutive uploads
- **Historical tracking** of ranking changes over time

**🏷️ File Type Classification:**
- **Standard Data Files**: Regular CSV imports
- **Ranking Snapshot Files**: Single chronological data points
- **Ranking Analysis Files**: Files with comparison data available

### **4. Storage Management**

**💾 Automatic Cleanup:**
- **Orphaned data removal** when files are deleted
- **Snapshot management** (keeps last 10 per category)
- **Comparison history** with automatic pruning
- **Storage optimization** to prevent clutter

**📊 Storage Monitoring:**
- **File size estimates** based on row counts
- **Total storage usage** across all files
- **Data distribution** by file and category
- **Performance impact** tracking

## 🚀 **How to Use File Management**

### **Step 1: Access File Manager**
1. Navigate to **Overview** → **All Data**
2. Click the **"Manage Files"** button (gray button with database icon)
3. File management modal opens with complete file inventory

### **Step 2: View File Information**
**File List Columns:**
- **☑️ Checkbox**: Select files for bulk operations
- **📄 File**: Filename and display name
- **🏷️ Type**: File classification with visual indicators
- **📂 Category**: Associated category (from filename or manual)
- **📊 Rows**: Total row count and estimated size
- **📅 Upload Date**: Precise upload timestamp
- **⚙️ Actions**: View details and delete options

### **Step 3: Filter and Search**
**🔍 Search Functionality:**
- **Filename search**: Find files by name or pattern
- **Real-time filtering**: Results update as you type
- **Case-insensitive**: Flexible search matching

**🏷️ Category Filtering:**
- **Dropdown filter**: Select specific categories
- **"All Categories"**: View all files regardless of category
- **Dynamic options**: Based on actual file categories

### **Step 4: Manage Individual Files**

**👁️ View File Details:**
1. Click the **eye icon** in the Actions column
2. Detailed view opens with comprehensive analysis:
   - **File information**: Name, type, upload date, size
   - **Data statistics**: Categories, authors, items, pricing
   - **Ranking analysis**: Snapshots and comparisons (if applicable)
   - **Content breakdown**: New items, ratings, categories

**🗑️ Delete Files:**
1. Click the **trash icon** in the Actions column
2. Confirmation dialog shows impact:
   - Number of data items to be removed
   - Associated ranking snapshots
   - Related ranking comparisons
3. Confirm deletion to permanently remove file and data

### **Step 5: Bulk Operations**

**📦 Select Multiple Files:**
- **Individual selection**: Check boxes next to specific files
- **Select all**: Use header checkbox to select all visible files
- **Filtered selection**: Select all files matching current filters

**🗑️ Bulk Delete:**
1. Select multiple files using checkboxes
2. **"Delete Selected (X)"** button appears
3. Click to delete all selected files
4. Confirmation shows total impact across all files

## 📊 **File Detail View Features**

### **📋 File Information Panel**
- **File metadata**: Name, type, category, upload date
- **Size information**: Row count and estimated file size
- **Type classification**: Visual indicators for file purpose
- **Category tags**: Associated data categories

### **📈 Data Statistics Dashboard**
- **Categories**: Number of unique categories in file
- **Authors**: Count of unique content creators
- **New Items**: Items marked with `is_new: true`
- **Ranking Items**: Items with position data

### **🔄 Ranking Analysis Section**
**For Ranking Files:**
- **Snapshots**: List of created ranking snapshots
- **Comparisons**: Generated ranking comparisons
- **Change tracking**: Number of ranking changes detected
- **Historical context**: Position in chronological sequence

### **💰 Content Statistics**
**When Available:**
- **Average Price**: Mean price across all items
- **Average Rating**: Mean rating with star display
- **Price distribution**: Range and statistics
- **Quality metrics**: Rating distribution

## 🔧 **Technical Implementation**

### **Data Source Tracking**
```typescript
interface DataSource {
  id: string;           // Unique identifier
  name: string;         // Display name
  filename: string;     // Original filename
  uploadDate: string;   // ISO timestamp
  rowCount: number;     // Total rows
  category?: string;    // Associated category
  competitor?: string;  // Associated competitor
}
```

### **Source-Data Relationship**
- **Metadata linking**: Each data item includes `sourceId` in metadata
- **Automatic cleanup**: Removing source removes all associated data
- **Referential integrity**: Maintains data consistency

### **Storage Structure**
```
localStorage:
  - dashboard_data_sources: Array of DataSource objects
  - dashboard_scraped_data: Array with sourceId metadata
  - dashboard_category_snapshots: Linked by filename
  - dashboard_ranking_comparisons: Linked by snapshot filenames
```

## 🎯 **Use Cases**

### **📊 Data Audit**
- **Review all imports**: See complete history of uploaded files
- **Identify data sources**: Track which file contributed which data
- **Analyze file patterns**: Understand data collection timeline

### **🧹 Storage Cleanup**
- **Remove old files**: Delete outdated or incorrect imports
- **Bulk cleanup**: Remove multiple test or duplicate files
- **Selective removal**: Keep important files, remove temporary ones

### **📈 Ranking Management**
- **Track chronological imports**: See sequence of ranking files
- **Manage snapshots**: Control ranking analysis data
- **Clean up comparisons**: Remove unnecessary ranking analyses

### **🔍 Data Investigation**
- **Source attribution**: Find which file contains specific data
- **Content analysis**: Understand file composition and quality
- **Impact assessment**: See what data would be lost before deletion

## ⚠️ **Important Notes**

### **🚨 Deletion Warning**
- **Permanent action**: File deletion cannot be undone
- **Cascading removal**: Deletes all associated data and analyses
- **Impact preview**: Always shows what will be removed
- **Confirmation required**: Multiple steps prevent accidental deletion

### **💾 Storage Considerations**
- **Browser storage**: Uses localStorage with size limitations
- **Performance impact**: Large numbers of files may slow operations
- **Automatic cleanup**: System manages storage optimization
- **Backup recommendation**: Export important data before cleanup

### **🔄 Ranking File Dependencies**
- **Chronological order**: Ranking comparisons depend on file sequence
- **Snapshot integrity**: Deleting files may break comparison chains
- **Category consistency**: Maintain consistent category naming
- **Filename patterns**: Follow naming conventions for automatic detection

## 🎉 **Benefits**

### **✅ Complete Control**
- **Full visibility**: See all uploaded files and their impact
- **Granular management**: Control individual files or bulk operations
- **Safe operations**: Confirmation dialogs prevent accidents

### **✅ Data Integrity**
- **Source tracking**: Always know where data originated
- **Consistent cleanup**: Removing files removes all related data
- **Referential integrity**: Maintains data consistency

### **✅ Storage Optimization**
- **Space management**: Remove unnecessary files to free storage
- **Performance**: Keep only needed data for optimal performance
- **Organization**: Maintain clean, organized data environment

### **✅ Analysis Support**
- **File statistics**: Understand content and quality of imports
- **Ranking insights**: Track chronological data patterns
- **Content metrics**: Analyze pricing, ratings, and categorization

---

**Your dashboard now provides complete control over uploaded CSV files with comprehensive management, tracking, and analysis capabilities!** 🚀
